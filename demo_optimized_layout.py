#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化界面布局演示
展示Material Design 3的美观设计和响应式布局
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QLabel, QFrame, QScrollArea, QSplitter
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

from src.gui.styles.unified_theme_system import UnifiedThemeSystem, ThemeMode
from src.gui.modern_ui_components import (
    MaterialButton, MaterialCard, MaterialLineEdit, MaterialTextEdit,
    MaterialComboBox, StatusIndicator, LoadingSpinner
)
from src.utils.logger import logger


class OptimizedLayoutDemo(QMainWindow):
    """优化布局演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.theme_system = UnifiedThemeSystem()
        self.init_ui()
        self.apply_theme()
        self.setup_demo_content()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("🎨 AI视频生成器 - 优化界面布局演示")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(16)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧导航面板
        nav_panel = self.create_navigation_panel()
        splitter.addWidget(nav_panel)
        
        # 中央内容区域
        content_area = self.create_content_area()
        splitter.addWidget(content_area)
        
        # 右侧信息面板
        info_panel = self.create_info_panel()
        splitter.addWidget(info_panel)
        
        # 设置分割器比例
        splitter.setSizes([300, 800, 300])
    
    def create_navigation_panel(self):
        """创建导航面板"""
        panel = MaterialCard()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)
        
        # 标题
        title = QLabel("🧭 导航菜单")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        layout.addWidget(title)
        
        # 导航按钮
        nav_buttons = [
            ("🎬 项目管理", "primary"),
            ("📝 分镜脚本", "flat"),
            ("🎨 图像生成", "flat"),
            ("🎵 配音合成", "flat"),
            ("🎞️ 视频制作", "flat"),
            ("⚙️ 系统设置", "flat"),
        ]
        
        for text, style in nav_buttons:
            btn = MaterialButton(text)
            btn.setMinimumHeight(48)
            if style == "primary":
                btn.setObjectName("primary_button")
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # 主题切换
        theme_btn = MaterialButton("🌙 切换主题")
        theme_btn.clicked.connect(self.toggle_theme)
        layout.addWidget(theme_btn)
        
        return panel
    
    def create_content_area(self):
        """创建内容区域"""
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # 内容容器
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)
        
        # 顶部工具栏卡片
        toolbar_card = self.create_toolbar_card()
        layout.addWidget(toolbar_card)
        
        # 主要内容网格
        content_grid = self.create_content_grid()
        layout.addWidget(content_grid)
        
        # 底部状态卡片
        status_card = self.create_status_card()
        layout.addWidget(status_card)
        
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        return scroll_area
    
    def create_toolbar_card(self):
        """创建工具栏卡片"""
        card = MaterialCard()
        layout = QHBoxLayout(card)
        layout.setContentsMargins(20, 16, 20, 16)
        
        # 标题
        title = QLabel("📋 项目工作台")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # 工具按钮
        tools = [
            ("➕ 新建项目", "primary"),
            ("📁 打开项目", "flat"),
            ("💾 保存项目", "success"),
            ("🔄 刷新", "flat"),
        ]
        
        for text, style in tools:
            btn = MaterialButton(text)
            btn.setMinimumHeight(40)
            if style == "primary":
                btn.setObjectName("primary_button")
            elif style == "success":
                btn.setObjectName("success_button")
            layout.addWidget(btn)
        
        return card
    
    def create_content_grid(self):
        """创建内容网格"""
        container = QWidget()
        grid = QGridLayout(container)
        grid.setSpacing(16)
        
        # 项目信息卡片
        project_card = self.create_project_info_card()
        grid.addWidget(project_card, 0, 0, 1, 2)
        
        # 快速操作卡片
        quick_actions_card = self.create_quick_actions_card()
        grid.addWidget(quick_actions_card, 0, 2)
        
        # 进度统计卡片
        progress_card = self.create_progress_card()
        grid.addWidget(progress_card, 1, 0)
        
        # 最近文件卡片
        recent_files_card = self.create_recent_files_card()
        grid.addWidget(recent_files_card, 1, 1)
        
        # 系统状态卡片
        system_status_card = self.create_system_status_card()
        grid.addWidget(system_status_card, 1, 2)
        
        return container
    
    def create_project_info_card(self):
        """创建项目信息卡片"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("📁 当前项目")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        layout.addWidget(title)
        
        # 项目名称输入
        name_input = MaterialLineEdit()
        name_input.setPlaceholderText("输入项目名称...")
        name_input.setText("我的AI视频项目")
        layout.addWidget(name_input)
        
        # 项目描述
        desc_input = MaterialTextEdit()
        desc_input.setPlaceholderText("项目描述...")
        desc_input.setMaximumHeight(100)
        desc_input.setPlainText("这是一个使用AI技术生成的精彩视频项目，包含智能分镜、自动配音和视觉效果。")
        layout.addWidget(desc_input)
        
        return card
    
    def create_quick_actions_card(self):
        """创建快速操作卡片"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("⚡ 快速操作")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        layout.addWidget(title)
        
        # 快速操作按钮
        actions = [
            "🎬 生成分镜",
            "🎨 创建图像",
            "🎵 合成配音",
            "🎞️ 导出视频"
        ]
        
        for action in actions:
            btn = MaterialButton(action)
            btn.setMinimumHeight(44)
            layout.addWidget(btn)
        
        layout.addStretch()
        
        return card
    
    def create_progress_card(self):
        """创建进度卡片"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("📊 项目进度")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        layout.addWidget(title)
        
        # 进度项目
        progress_items = [
            ("分镜脚本", 85, "success"),
            ("图像生成", 60, "primary"),
            ("配音合成", 30, "warning"),
            ("视频制作", 10, "info")
        ]
        
        for name, value, status in progress_items:
            item_layout = QHBoxLayout()
            
            label = QLabel(name)
            item_layout.addWidget(label)
            
            item_layout.addStretch()
            
            percent_label = QLabel(f"{value}%")
            percent_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
            item_layout.addWidget(percent_label)
            
            layout.addLayout(item_layout)
        
        return card
    
    def create_recent_files_card(self):
        """创建最近文件卡片"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("📄 最近文件")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        layout.addWidget(title)
        
        # 文件列表
        files = [
            "🎬 春日物语.mp4",
            "🎨 角色设计.png",
            "📝 剧本草稿.txt",
            "🎵 背景音乐.mp3"
        ]
        
        for file_name in files:
            file_btn = MaterialButton(file_name)
            file_btn.setMinimumHeight(36)
            file_btn.setObjectName("flat_button")
            layout.addWidget(file_btn)
        
        layout.addStretch()
        
        return card
    
    def create_system_status_card(self):
        """创建系统状态卡片"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("🖥️ 系统状态")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        layout.addWidget(title)
        
        # 状态指示器
        status_layout = QVBoxLayout()
        
        # GPU状态
        gpu_layout = QHBoxLayout()
        gpu_layout.addWidget(QLabel("GPU:"))
        gpu_layout.addStretch()
        gpu_status = StatusIndicator("active")
        gpu_layout.addWidget(gpu_status)
        status_layout.addLayout(gpu_layout)
        
        # 内存状态
        memory_layout = QHBoxLayout()
        memory_layout.addWidget(QLabel("内存:"))
        memory_layout.addStretch()
        memory_status = StatusIndicator("warning")
        memory_layout.addWidget(memory_status)
        status_layout.addLayout(memory_layout)
        
        # 网络状态
        network_layout = QHBoxLayout()
        network_layout.addWidget(QLabel("网络:"))
        network_layout.addStretch()
        network_status = StatusIndicator("active")
        network_layout.addWidget(network_status)
        status_layout.addLayout(network_layout)
        
        layout.addLayout(status_layout)
        layout.addStretch()
        
        return card
    
    def create_info_panel(self):
        """创建信息面板"""
        panel = MaterialCard()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)
        
        # 标题
        title = QLabel("ℹ️ 信息面板")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        layout.addWidget(title)
        
        # 通知卡片
        notification_card = MaterialCard()
        notif_layout = QVBoxLayout(notification_card)
        notif_layout.setContentsMargins(12, 12, 12, 12)
        
        notif_title = QLabel("🔔 通知")
        notif_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        notif_layout.addWidget(notif_title)
        
        notifications = [
            "✅ 图像生成完成",
            "⏳ 配音合成中...",
            "📢 系统更新可用"
        ]
        
        for notif in notifications:
            notif_label = QLabel(notif)
            notif_label.setWordWrap(True)
            notif_layout.addWidget(notif_label)
        
        layout.addWidget(notification_card)
        
        # 帮助卡片
        help_card = MaterialCard()
        help_layout = QVBoxLayout(help_card)
        help_layout.setContentsMargins(12, 12, 12, 12)
        
        help_title = QLabel("❓ 帮助")
        help_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        help_layout.addWidget(help_title)
        
        help_btn = MaterialButton("📖 查看文档")
        help_layout.addWidget(help_btn)
        
        tutorial_btn = MaterialButton("🎓 新手教程")
        help_layout.addWidget(tutorial_btn)
        
        layout.addWidget(help_card)
        
        layout.addStretch()
        
        return panel
    
    def create_status_card(self):
        """创建状态卡片"""
        card = MaterialCard()
        layout = QHBoxLayout(card)
        layout.setContentsMargins(20, 12, 20, 12)
        
        # 状态信息
        status_label = QLabel("✅ 系统运行正常")
        layout.addWidget(status_label)
        
        layout.addStretch()
        
        # 加载指示器
        loading_spinner = LoadingSpinner()
        loading_spinner.start_animation()
        layout.addWidget(loading_spinner)
        
        # 版本信息
        version_label = QLabel("v2.0.0")
        version_label.setFont(QFont("Microsoft YaHei", 10))
        layout.addWidget(version_label)
        
        return card
    
    def apply_theme(self):
        """应用主题"""
        try:
            self.theme_system.apply_to_widget(self)
            
            # 自定义样式增强
            custom_style = """
                QMainWindow {
                    background-color: var(--md-sys-color-background);
                }
                
                QScrollArea {
                    border: none;
                    background-color: transparent;
                }
                
                QSplitter::handle {
                    background-color: var(--md-sys-color-outline-variant);
                    width: 2px;
                }
                
                QSplitter::handle:hover {
                    background-color: var(--md-sys-color-primary);
                }
            """
            
            self.setStyleSheet(self.styleSheet() + custom_style)
            
            logger.info("主题应用成功")
        except Exception as e:
            logger.error(f"主题应用失败: {e}")
    
    def toggle_theme(self):
        """切换主题"""
        try:
            current_mode = self.theme_system.get_current_mode()
            new_mode = ThemeMode.DARK if current_mode == ThemeMode.LIGHT else ThemeMode.LIGHT
            self.theme_system.set_theme_mode(new_mode)
            self.apply_theme()
            logger.info(f"主题切换到: {new_mode.value}")
        except Exception as e:
            logger.error(f"主题切换失败: {e}")
    
    def setup_demo_content(self):
        """设置演示内容"""
        # 定时器用于模拟动态更新
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_demo_content)
        self.update_timer.start(5000)  # 每5秒更新一次
    
    def update_demo_content(self):
        """更新演示内容"""
        # 这里可以添加动态内容更新逻辑
        pass


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("AI视频生成器 - 优化布局演示")
    app.setApplicationVersion("2.0")
    
    # 创建演示窗口
    window = OptimizedLayoutDemo()
    window.show()
    
    logger.info("优化布局演示启动成功")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()