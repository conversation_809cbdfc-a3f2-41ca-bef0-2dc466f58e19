#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理时间戳文件夹脚本
删除所有带时间戳的文件夹，并将内容合并到对应的主文件夹中
"""

import os
import shutil
import re
from pathlib import Path
from typing import List, Dict

def is_timestamp_folder(folder_name: str) -> bool:
    """检查是否是时间戳文件夹"""
    timestamp_pattern = r'.*_\d{8}_\d{6}$'
    return bool(re.match(timestamp_pattern, folder_name))

def get_base_name(folder_name: str) -> str:
    """获取文件夹的基础名称（去除时间戳）"""
    timestamp_pattern = r'_\d{8}_\d{6}$'
    return re.sub(timestamp_pattern, '', folder_name)

def merge_folder_contents(source_dir: Path, target_dir: Path) -> bool:
    """将源文件夹内容合并到目标文件夹"""
    try:
        if not source_dir.exists() or not source_dir.is_dir():
            return False
            
        # 确保目标文件夹存在
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # 遍历源文件夹中的所有内容
        for item in source_dir.iterdir():
            target_item = target_dir / item.name
            
            if item.is_file():
                # 如果目标文件不存在，直接移动
                if not target_item.exists():
                    shutil.move(str(item), str(target_item))
                    print(f"  移动文件: {item.name}")
                else:
                    print(f"  跳过已存在的文件: {item.name}")
            elif item.is_dir():
                # 递归合并子文件夹
                if target_item.exists():
                    merge_folder_contents(item, target_item)
                else:
                    shutil.move(str(item), str(target_item))
                    print(f"  移动文件夹: {item.name}")
        
        return True
    except Exception as e:
        print(f"合并文件夹失败: {e}")
        return False

def cleanup_timestamp_folders(base_dir: str = "output") -> List[str]:
    """清理时间戳文件夹"""
    base_path = Path(base_dir)
    removed_folders = []
    
    if not base_path.exists():
        print(f"目录不存在: {base_dir}")
        return removed_folders
    
    print(f"开始清理 {base_dir} 目录中的时间戳文件夹...")
    
    # 遍历所有项目文件夹
    for project_dir in base_path.iterdir():
        if not project_dir.is_dir():
            continue
            
        print(f"\n检查项目: {project_dir.name}")
        
        # 收集该项目下的所有时间戳文件夹
        timestamp_folders = []
        for item in project_dir.iterdir():
            if item.is_dir() and is_timestamp_folder(item.name):
                timestamp_folders.append(item)
        
        if not timestamp_folders:
            print("  没有发现时间戳文件夹")
            continue
            
        print(f"  发现 {len(timestamp_folders)} 个时间戳文件夹")
        
        # 按基础名称分组
        folder_groups: Dict[str, List[Path]] = {}
        for folder in timestamp_folders:
            base_name = get_base_name(folder.name)
            if base_name not in folder_groups:
                folder_groups[base_name] = []
            folder_groups[base_name].append(folder)
        
        # 处理每个组
        for base_name, folders in folder_groups.items():
            target_folder = project_dir / base_name
            
            print(f"  处理文件夹组: {base_name}")
            
            # 确保目标文件夹存在
            target_folder.mkdir(exist_ok=True)
            
            # 合并所有时间戳文件夹的内容到目标文件夹
            for folder in folders:
                print(f"    合并: {folder.name} -> {base_name}")
                if merge_folder_contents(folder, target_folder):
                    # 删除空的时间戳文件夹
                    try:
                        if not any(folder.iterdir()):  # 检查文件夹是否为空
                            folder.rmdir()
                            removed_folders.append(str(folder))
                            print(f"    已删除空文件夹: {folder.name}")
                        else:
                            print(f"    文件夹不为空，跳过删除: {folder.name}")
                    except Exception as e:
                        print(f"    删除文件夹失败 {folder.name}: {e}")
    
    return removed_folders

def main():
    """主函数"""
    print("=" * 60)
    print("时间戳文件夹清理工具")
    print("=" * 60)
    
    # 清理时间戳文件夹
    removed = cleanup_timestamp_folders()
    
    print(f"\n清理完成！")
    print(f"共删除 {len(removed)} 个时间戳文件夹:")
    for folder in removed:
        print(f"  - {folder}")
    
    if removed:
        print("\n建议:")
        print("- 检查合并后的文件夹内容是否正确")
        print("- 如有问题，可以从备份中恢复")
    else:
        print("\n没有发现需要清理的时间戳文件夹")

if __name__ == "__main__":
    main()