#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖测试脚本
检查程序运行所需的依赖包
"""

import sys
import traceback
import importlib

def test_import(module_name, package_name=None):
    """测试导入模块"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {package_name or module_name} - 导入成功")
        return True
    except ImportError as e:
        print(f"❌ {package_name or module_name} - 导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️  {package_name or module_name} - 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 基本功能测试:")

    # 测试文件操作
    try:
        import os
        import json
        from pathlib import Path

        # 测试配置文件读取
        config_path = Path("config/llm_config.json")
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ 配置文件读取成功")
            print(f"   发现 {len(config.get('models', []))} 个AI模型配置")
        else:
            print("⚠️  配置文件不存在")

    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")

    # 测试网络功能
    try:
        import requests
        # 简单的网络连接测试
        response = requests.get("https://httpbin.org/get", timeout=5)
        if response.status_code == 200:
            print("✅ 网络连接测试成功")
        else:
            print("⚠️  网络连接异常")
    except ImportError:
        print("❌ requests模块未安装，无法测试网络功能")
    except Exception as e:
        print(f"⚠️  网络测试失败: {e}")

def main():
    print("🔍 AI视频生成器 - 依赖检查")
    print("=" * 50)

    # 检查Python版本
    print(f"Python版本: {sys.version}")
    print(f"平台: {sys.platform}")
    print()

    # 核心依赖检查
    dependencies = [
        ("PyQt5", "PyQt5"),
        ("PyQt5.QtWidgets", "PyQt5.QtWidgets"),
        ("PyQt5.QtCore", "PyQt5.QtCore"),
        ("requests", "requests"),
        ("PIL", "Pillow"),
        ("numpy", "numpy"),
        ("json", "json (内置)"),
        ("os", "os (内置)"),
        ("sys", "sys (内置)"),
        ("pathlib", "pathlib (内置)"),
    ]

    success_count = 0
    total_count = len(dependencies)

    print("📦 核心依赖检查:")
    for module, package in dependencies:
        if test_import(module, package):
            success_count += 1

    print()
    print("🔧 可选依赖检查:")
    optional_dependencies = [
        ("aiohttp", "aiohttp"),
        ("aiofiles", "aiofiles"),
        ("moviepy", "moviepy"),
        ("pydub", "pydub"),
        ("edge_tts", "edge-tts"),
        ("pandas", "pandas"),
        ("tqdm", "tqdm"),
        ("colorama", "colorama"),
        ("jieba", "jieba"),
    ]

    optional_success = 0
    for module, package in optional_dependencies:
        if test_import(module, package):
            optional_success += 1

    # 测试基本功能
    test_basic_functionality()

    print()
    print("📊 检查结果:")
    print(f"核心依赖: {success_count}/{total_count} 成功")
    print(f"可选依赖: {optional_success}/{len(optional_dependencies)} 成功")

    if success_count >= 4:  # 至少需要PyQt5, requests, PIL, numpy
        print("✅ 基本依赖满足，可以尝试运行程序")

        # 尝试导入主程序模块
        print("\n🎬 测试主程序导入:")
        try:
            # 先测试核心模块
            from src.utils.logger import logger
            print("✅ 日志模块导入成功")

            from src.core.project_manager import ProjectManager
            print("✅ 项目管理器导入成功")

            # 如果PyQt5可用，测试GUI模块
            if success_count >= 6:  # 包括PyQt5相关模块
                from src.gui.new_main_window import NewMainWindow
                print("✅ 主窗口模块导入成功")
            else:
                print("⚠️  PyQt5未完全安装，跳过GUI模块测试")

        except Exception as e:
            print(f"❌ 主程序模块导入失败: {e}")
            print("详细错误信息:")
            traceback.print_exc()

    else:
        print("❌ 核心依赖不足，需要安装缺失的包")
        print("\n📝 安装建议:")
        print("pip install PyQt5 PyQtWebEngine requests Pillow numpy pandas tqdm colorama")

    print("\n🚀 下一步:")
    if success_count >= 6:
        print("可以尝试运行: python main.py")
    elif success_count >= 4:
        print("等待PyQt5安装完成后运行: python main.py")
    else:
        print("请先安装缺失的依赖包")

if __name__ == "__main__":
    main()
