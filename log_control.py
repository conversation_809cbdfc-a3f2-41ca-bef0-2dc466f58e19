#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志控制工具
用于动态调整应用程序的日志输出级别

使用方法:
  python log_control.py --mode normal     # 正常模式（推荐）
  python log_control.py --mode quiet      # 安静模式（只显示错误）
  python log_control.py --mode verbose    # 详细模式（显示所有日志）
  python log_control.py --console WARNING # 设置控制台日志级别
  python log_control.py --file DEBUG      # 设置文件日志级别
  python log_control.py --status          # 查看当前配置
  python log_control.py --reset           # 重置为默认配置
"""

import argparse
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from src.utils.log_config_manager import (
        log_config_manager,
        enable_verbose_mode,
        enable_quiet_mode,
        enable_normal_mode,
        set_console_level,
        set_file_level,
        get_config_summary,
        reset_to_default
    )
except ImportError as e:
    print(f"导入日志配置管理器失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="AI视频生成器日志控制工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
日志级别说明:
  DEBUG    - 显示所有调试信息（最详细）
  INFO     - 显示一般信息
  WARNING  - 只显示警告和错误（推荐）
  ERROR    - 只显示错误信息
  CRITICAL - 只显示严重错误

模式说明:
  normal   - 控制台显示WARNING及以上，文件记录所有（推荐）
  quiet    - 控制台只显示ERROR，文件记录WARNING及以上
  verbose  - 控制台和文件都显示所有日志（调试用）
        """
    )
    
    parser.add_argument(
        '--mode', 
        choices=['normal', 'quiet', 'verbose'],
        help='设置日志模式'
    )
    
    parser.add_argument(
        '--console',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        help='设置控制台日志级别'
    )
    
    parser.add_argument(
        '--file',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        help='设置文件日志级别'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示当前日志配置'
    )
    
    parser.add_argument(
        '--reset',
        action='store_true',
        help='重置为默认配置'
    )
    
    args = parser.parse_args()
    
    # 如果没有提供任何参数，显示帮助
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    try:
        # 处理模式设置
        if args.mode:
            if args.mode == 'normal':
                enable_normal_mode()
            elif args.mode == 'quiet':
                enable_quiet_mode()
            elif args.mode == 'verbose':
                enable_verbose_mode()
        
        # 处理控制台级别设置
        if args.console:
            set_console_level(args.console)
            print(f"控制台日志级别已设置为: {args.console}")
        
        # 处理文件级别设置
        if args.file:
            set_file_level(args.file)
            print(f"文件日志级别已设置为: {args.file}")
        
        # 重置配置
        if args.reset:
            reset_to_default()
        
        # 显示状态
        if args.status or not any([args.mode, args.console, args.file, args.reset]):
            print(get_config_summary())
            
    except Exception as e:
        print(f"操作失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()