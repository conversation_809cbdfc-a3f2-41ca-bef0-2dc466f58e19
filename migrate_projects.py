#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目数据迁移脚本
清理重复的时间戳文件夹，统一项目数据到project.json
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.project_data_migrator import ProjectDataMigrator
from src.utils.logger import logger


def main():
    """主函数"""
    print("=" * 60)
    print("项目数据迁移工具")
    print("=" * 60)
    
    # 创建迁移器
    migrator = ProjectDataMigrator("output")
    
    print("\n1. 检查并清理重复的时间戳文件夹...")
    removed_folders = migrator.remove_duplicate_timestamp_folders()
    
    if removed_folders:
        print(f"已清理 {len(removed_folders)} 个重复的时间戳文件夹:")
        for folder in removed_folders:
            print(f"  - {folder}")
    else:
        print("未发现重复的时间戳文件夹")
    
    print("\n2. 迁移项目数据到统一格式...")
    migrated_projects = migrator.migrate_all_projects()
    
    if migrated_projects:
        print(f"已迁移 {len(migrated_projects)} 个项目:")
        for project in migrated_projects:
            print(f"  - {project}")
    else:
        print("未发现需要迁移的项目")
    
    print("\n3. 检查迁移结果...")
    output_dir = Path("output")
    if output_dir.exists():
        projects = []
        for item in output_dir.iterdir():
            if item.is_dir():
                project_json = item / "project.json"
                if project_json.exists():
                    projects.append(item.name)
        
        if projects:
            print(f"发现 {len(projects)} 个有效项目:")
            for project in projects:
                print(f"  ✓ {project}")
        else:
            print("未发现有效项目")
    
    print("\n迁移完成！")
    print("\n注意事项:")
    print("- 所有项目数据现在统一保存在各项目文件夹的 project.json 中")
    print("- 配音、字幕、绘图、视频等所有数据都记录在同一个文件中")
    print("- 不再创建重复的时间戳文件夹")
    print("- 如果项目文件夹已存在，会直接使用现有文件夹")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        logger.error(f"迁移过程中发生错误: {e}")
        print(f"\n错误: {e}")
        sys.exit(1)
