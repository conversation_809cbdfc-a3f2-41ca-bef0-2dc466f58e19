#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试现代化卡片式界面
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def main():
    """主函数"""
    # 设置Qt属性
    try:
        QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
    except AttributeError:
        pass
    
    app = QApplication(sys.argv)
    
    try:
        # 导入现代化卡片式主窗口
        from src.gui.modern_card_main_window import ModernCardMainWindow
        
        print("正在启动现代化卡片式界面...")
        
        # 设置应用程序样式
        app.setStyle("Fusion")
        
        # 创建并显示主窗口
        main_window = ModernCardMainWindow()
        main_window.show()
        
        print("现代化界面已启动")
        
        # 启动事件循环
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"导入失败: {e}")
        QMessageBox.critical(None, "错误", f"无法启动界面: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        QMessageBox.critical(None, "错误", f"启动时发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
