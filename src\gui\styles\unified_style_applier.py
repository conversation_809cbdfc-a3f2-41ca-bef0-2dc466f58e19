#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一样式应用器
替换原有的多个样式系统，提供简洁统一的接口
"""

from typing import Optional
from PyQt5.QtWidgets import QApplication, QWidget
from PyQt5.QtCore import QObject, pyqtSignal

from .theme_manager import get_theme_manager, ThemeManager
from .color_schemes import ColorSchemeType, ThemeMode
from src.utils.logger import logger


class UnifiedStyleApplier(QObject):
    """统一样式应用器"""
    
    # 信号
    style_applied = pyqtSignal(str)  # 样式应用完成信号
    
    def __init__(self):
        super().__init__()
        self.theme_manager = get_theme_manager()
        
        # 连接主题管理器信号
        self.theme_manager.theme_changed.connect(self._on_theme_changed)
        
        logger.info("统一样式应用器初始化完成")
    
    def apply_to_application(self, app: Optional[QApplication] = None):
        """应用样式到整个应用程序"""
        try:
            self.theme_manager.apply_to_application(app)
            self.style_applied.emit("application")
            logger.info("样式已应用到应用程序")
        except Exception as e:
            logger.error(f"应用应用程序样式失败: {e}")
    
    def apply_to_widget(self, widget: QWidget):
        """应用样式到指定控件"""
        try:
            self.theme_manager.apply_to_widget(widget)
            self.style_applied.emit(f"widget_{widget.__class__.__name__}")
            logger.debug(f"样式已应用到控件: {widget.__class__.__name__}")
        except Exception as e:
            logger.error(f"应用控件样式失败: {e}")
    
    def set_color_scheme(self, scheme_type: ColorSchemeType):
        """设置配色方案"""
        try:
            self.theme_manager.set_color_scheme(scheme_type)
            logger.info(f"配色方案已设置: {scheme_type.value}")
        except Exception as e:
            logger.error(f"设置配色方案失败: {e}")
    
    def set_theme_mode(self, mode: ThemeMode):
        """设置主题模式"""
        try:
            self.theme_manager.set_theme_mode(mode)
            logger.info(f"主题模式已设置: {mode.value}")
        except Exception as e:
            logger.error(f"设置主题模式失败: {e}")
    
    def toggle_theme_mode(self):
        """切换主题模式"""
        try:
            self.theme_manager.toggle_theme_mode()
            current_mode = self.theme_manager.current_mode
            logger.info(f"主题模式已切换到: {current_mode.value}")
        except Exception as e:
            logger.error(f"切换主题模式失败: {e}")
    
    def get_current_color(self, key: str, fallback: str = "#000000") -> str:
        """获取当前主题颜色"""
        return self.theme_manager.get_color(key, fallback)
    
    def get_current_scheme_info(self) -> dict:
        """获取当前主题信息"""
        return self.theme_manager.get_current_scheme_info()
    
    def get_available_schemes(self) -> dict:
        """获取可用的配色方案"""
        return self.theme_manager.get_available_schemes()
    
    def _on_theme_changed(self, scheme_name: str, mode_name: str):
        """主题变化处理"""
        logger.info(f"主题已变化: {scheme_name} - {mode_name}")
        # 自动重新应用样式到应用程序
        self.apply_to_application()


# 全局样式应用器实例
_style_applier: Optional[UnifiedStyleApplier] = None


def get_style_applier() -> UnifiedStyleApplier:
    """获取全局样式应用器实例"""
    global _style_applier
    if _style_applier is None:
        _style_applier = UnifiedStyleApplier()
    return _style_applier


def apply_modern_style(widget: Optional[QWidget] = None):
    """应用现代化样式（兼容性函数）"""
    applier = get_style_applier()
    if widget is None:
        applier.apply_to_application()
    else:
        applier.apply_to_widget(widget)


def toggle_theme():
    """切换主题（兼容性函数）"""
    applier = get_style_applier()
    applier.toggle_theme_mode()


def set_color_scheme(scheme_type: ColorSchemeType):
    """设置配色方案（兼容性函数）"""
    applier = get_style_applier()
    applier.set_color_scheme(scheme_type)


def set_theme_mode(mode: ThemeMode):
    """设置主题模式（兼容性函数）"""
    applier = get_style_applier()
    applier.set_theme_mode(mode)


def get_current_color(key: str, fallback: str = "#000000") -> str:
    """获取当前主题颜色（兼容性函数）"""
    applier = get_style_applier()
    return applier.get_current_color(key, fallback)


# 为了向后兼容，提供一些别名函数
def apply_theme_to_application(app: Optional[QApplication] = None):
    """应用主题到应用程序"""
    applier = get_style_applier()
    applier.apply_to_application(app)


def apply_theme_to_widget(widget: QWidget):
    """应用主题到控件"""
    applier = get_style_applier()
    applier.apply_to_widget(widget)


# 主题切换的便捷函数
def switch_to_ocean_blue():
    """切换到深海蓝主题"""
    set_color_scheme(ColorSchemeType.OCEAN_BLUE)


def switch_to_forest_green():
    """切换到森林绿主题"""
    set_color_scheme(ColorSchemeType.FOREST_GREEN)


def switch_to_violet():
    """切换到紫罗兰主题"""
    set_color_scheme(ColorSchemeType.VIOLET)


def switch_to_warm_orange():
    """切换到暖橙主题"""
    set_color_scheme(ColorSchemeType.WARM_ORANGE)


def switch_to_graphite():
    """切换到石墨灰主题"""
    set_color_scheme(ColorSchemeType.GRAPHITE)


def switch_to_light_mode():
    """切换到浅色模式"""
    set_theme_mode(ThemeMode.LIGHT)


def switch_to_dark_mode():
    """切换到深色模式"""
    set_theme_mode(ThemeMode.DARK)


# 样式预设应用函数
def apply_business_theme():
    """应用商务主题（石墨灰）"""
    switch_to_graphite()


def apply_nature_theme():
    """应用自然主题（森林绿）"""
    switch_to_forest_green()


def apply_creative_theme():
    """应用创意主题（紫罗兰）"""
    switch_to_violet()


def apply_energetic_theme():
    """应用活力主题（暖橙）"""
    switch_to_warm_orange()


def apply_professional_theme():
    """应用专业主题（深海蓝）"""
    switch_to_ocean_blue()


# 获取主题信息的便捷函数
def get_theme_info() -> dict:
    """获取当前主题信息"""
    applier = get_style_applier()
    return applier.get_current_scheme_info()


def get_available_color_schemes() -> dict:
    """获取可用的配色方案"""
    applier = get_style_applier()
    return applier.get_available_schemes()


def is_dark_mode() -> bool:
    """检查是否为深色模式"""
    applier = get_style_applier()
    return applier.theme_manager.current_mode == ThemeMode.DARK


def is_light_mode() -> bool:
    """检查是否为浅色模式"""
    applier = get_style_applier()
    return applier.theme_manager.current_mode == ThemeMode.LIGHT


# 样式刷新函数
def refresh_application_style():
    """刷新应用程序样式"""
    applier = get_style_applier()
    applier.apply_to_application()


def refresh_widget_style(widget: QWidget):
    """刷新控件样式"""
    applier = get_style_applier()
    applier.apply_to_widget(widget)
