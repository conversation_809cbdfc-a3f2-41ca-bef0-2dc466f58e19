#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度测试五阶段分镜系统
"""

import sys
import os
import asyncio
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_storyboard_llm_wrapper():
    """测试五阶段分镜系统的LLM包装器"""
    print("🎬 深度测试: 五阶段分镜系统LLM包装器")
    
    try:
        from src.gui.five_stage_storyboard_tab import FiveStageStoryboardTab
        from src.core.service_manager import ServiceManager, ServiceType
        from PyQt5.QtWidgets import QApplication, QWidget
        import sys
        
        # 创建QApplication（如果不存在）
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建父窗口
        parent = QWidget()
        
        # 初始化五阶段分镜标签页
        storyboard_tab = FiveStageStoryboardTab(parent)
        print("  ✓ 五阶段分镜标签页创建成功")
        
        # 模拟选择模型并初始化
        # 获取可用模型
        from src.utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        models = config_manager.config.get("models", [])
        
        if not models:
            print("  ❌ 没有找到可用的模型配置")
            return False
            
        # 使用第一个模型
        selected_model = models[0].get("name", "unknown")
        print(f"  📋 使用模型: {selected_model}")
        
        # 模拟初始化过程
        model_config = models[0]
        
        # 使用服务管理器获取LLM服务
        from src.core.service_manager import ServiceManager, ServiceType
        service_manager = ServiceManager()
        llm_service = service_manager.get_service(ServiceType.LLM)
        
        if not llm_service:
            print("  ❌ LLM服务获取失败")
            return False
            
        print("  ✓ LLM服务获取成功")
        
        # 创建兼容性包装器（模拟五阶段分镜标签页中的代码）
        class LLMApiWrapper:
            def __init__(self, llm_service):
                self.llm_service = llm_service
                self.shots_model_name = "default"
                
            def _make_api_call(self, model_name, messages, task_name):
                """兼容旧API调用的包装器"""
                import asyncio
                
                # 提取用户消息内容
                user_content = ""
                for msg in messages:
                    if msg.get("role") == "user":
                        user_content = msg.get("content", "")
                        break
                
                if not user_content:
                    return "错误：未找到有效的提示内容"
                
                # 创建事件循环并调用LLM服务
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    result = loop.run_until_complete(
                        self.llm_service.generate_text(user_content)
                    )
                    loop.close()
                    
                    if result.success:
                        return result.data.get('content', '')
                    else:
                        return f"API调用失败: {result.error}"
                except Exception as e:
                    return f"API调用异常: {str(e)}"
        
        # 测试包装器
        wrapper = LLMApiWrapper(llm_service)
        print("  ✓ LLM包装器创建成功")
        
        # 测试API调用
        test_messages = [
            {"role": "system", "content": "你是一位专业的分镜师。"},
            {"role": "user", "content": "请为以下故事生成分镜：小猫咪在花园里玩耍。"}
        ]
        
        print("  🧪 测试API调用...")
        result = wrapper._make_api_call("test_model", test_messages, "test_task")
        
        if result and not result.startswith("错误") and not result.startswith("API调用失败"):
            print(f"  ✅ API调用成功，返回内容长度: {len(result)}")
            print(f"  📄 返回内容预览: {result[:100]}...")
            return True
        else:
            print(f"  ❌ API调用失败: {result}")
            return False
            
    except Exception as e:
        print(f"  ❌ 深度测试失败: {e}")
        traceback.print_exc()
        return False

def test_complete_workflow():
    """测试完整工作流程"""
    print("\n🔄 完整工作流程测试")
    
    try:
        # 1. 创建故事
        print("  📝 步骤1: AI创作故事")
        from src.core.service_manager import ServiceManager, ServiceType
        
        service_manager = ServiceManager()
        llm_service = service_manager.get_service(ServiceType.LLM)
        
        if not llm_service:
            print("    ❌ LLM服务不可用")
            return False
        
        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 创作故事
            story_result = loop.run_until_complete(
                llm_service.create_story_from_theme("勇敢的小猫咪")
            )
            
            if not story_result.success:
                print(f"    ❌ 故事创作失败: {story_result.error}")
                return False
                
            story_content = story_result.data.get('content', '')
            print(f"    ✅ 故事创作成功，长度: {len(story_content)}")
            
            # 2. 生成分镜
            print("  🎬 步骤2: 生成分镜脚本")
            storyboard_result = loop.run_until_complete(
                llm_service.generate_storyboard(story_content[:500])  # 使用故事前500字符
            )
            
            if storyboard_result.success:
                storyboard_content = storyboard_result.data.get('content', '')
                print(f"    ✅ 分镜生成成功，长度: {len(storyboard_content)}")
            else:
                print(f"    ⚠️ 分镜生成失败: {storyboard_result.error}")
            
            # 3. 测试图像生成服务
            print("  🖼️ 步骤3: 测试图像生成服务")
            image_service = service_manager.get_service(ServiceType.IMAGE)
            if image_service:
                print("    ✅ 图像服务可用")
            else:
                print("    ⚠️ 图像服务不可用")
            
            # 4. 测试语音生成服务
            print("  🎤 步骤4: 测试语音生成服务")
            voice_service = service_manager.get_service(ServiceType.VOICE)
            if voice_service:
                print("    ✅ 语音服务可用")
            else:
                print("    ⚠️ 语音服务不可用")
            
            print("  🎉 完整工作流程测试完成")
            return True
            
        finally:
            loop.close()
            
    except Exception as e:
        print(f"  ❌ 完整工作流程测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始深度测试")
    print("=" * 50)
    
    # 测试1: 五阶段分镜系统包装器
    wrapper_result = test_storyboard_llm_wrapper()
    
    # 测试2: 完整工作流程
    workflow_result = test_complete_workflow()
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 深度测试结果:")
    print(f"  五阶段分镜包装器: {'✅ 通过' if wrapper_result else '❌ 失败'}")
    print(f"  完整工作流程: {'✅ 通过' if workflow_result else '❌ 失败'}")
    
    if wrapper_result and workflow_result:
        print("\n🎉 所有深度测试通过！系统工作正常。")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    main()
