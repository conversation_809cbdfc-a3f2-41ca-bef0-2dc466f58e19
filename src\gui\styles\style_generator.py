#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样式生成器
根据配色方案生成完整的QSS样式表
支持Material Design和Fluent Design规范
"""

from typing import Dict, List
from .color_schemes import ColorScheme


class StyleGenerator:
    """样式生成器"""
    
    def __init__(self):
        self.component_generators = {
            'base': self._generate_base_styles,
            'buttons': self._generate_button_styles,
            'inputs': self._generate_input_styles,
            'lists': self._generate_list_styles,
            'tables': self._generate_table_styles,
            'tabs': self._generate_tab_styles,
            'scrollbars': self._generate_scrollbar_styles,
            'menus': self._generate_menu_styles,
            'dialogs': self._generate_dialog_styles,
            'progress': self._generate_progress_styles,
        }
    
    def generate_complete_stylesheet(self, scheme: ColorScheme) -> str:
        """生成完整的样式表"""
        styles = []
        
        # 生成各个组件的样式
        for component_name, generator in self.component_generators.items():
            try:
                component_style = generator(scheme)
                if component_style:
                    styles.append(f"/* {component_name.upper()} STYLES */")
                    styles.append(component_style)
                    styles.append("")  # 添加空行分隔
            except Exception as e:
                print(f"生成{component_name}样式失败: {e}")
        
        return "\n".join(styles)
    
    def _generate_base_styles(self, scheme: ColorScheme) -> str:
        """生成基础样式"""
        return f"""
        /* 全局样式 */
        QWidget {{
            background-color: {scheme.get_color('background')};
            color: {scheme.get_color('on_background')};
            font-family: "Microsoft YaHei UI", "Segoe UI", "Microsoft YaHei", Arial, sans-serif;
            font-size: 14px;
            selection-background-color: {scheme.get_color('primary_container')};
            selection-color: {scheme.get_color('on_primary_container')};
        }}
        
        QMainWindow {{
            background-color: {scheme.get_color('background')};
            color: {scheme.get_color('on_background')};
        }}
        
        QFrame {{
            background-color: {scheme.get_color('surface')};
            color: {scheme.get_color('on_surface')};
            border: 1px solid {scheme.get_color('outline_variant')};
            border-radius: 8px;
        }}
        
        QLabel {{
            background-color: transparent;
            color: {scheme.get_color('on_surface')};
            border: none;
        }}
        
        QGroupBox {{
            background-color: {scheme.get_color('surface_container')};
            color: {scheme.get_color('on_surface')};
            border: 2px solid {scheme.get_color('outline_variant')};
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            font-weight: bold;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: {scheme.get_color('primary')};
            background-color: {scheme.get_color('surface_container')};
        }}
        """
    
    def _generate_button_styles(self, scheme: ColorScheme) -> str:
        """生成按钮样式"""
        return f"""
        /* 按钮样式 */
        QPushButton {{
            background-color: {scheme.get_color('primary')};
            color: {scheme.get_color('on_primary')};
            border: none;
            border-radius: 20px;
            padding: 12px 24px;
            font-weight: 500;
            font-size: 14px;
            min-height: 20px;
        }}
        
        QPushButton:hover {{
            background-color: {scheme.get_color('primary_container')};
            color: {scheme.get_color('on_primary_container')};
            border: 2px solid {scheme.get_color('primary')};
        }}
        
        QPushButton:pressed {{
            background-color: {scheme.get_color('primary')};
            color: {scheme.get_color('on_primary')};
            border: 2px solid {scheme.get_color('primary_container')};
        }}
        
        QPushButton:disabled {{
            background-color: {scheme.get_color('surface_variant')};
            color: {scheme.get_color('on_surface_variant')};
            border: none;
        }}
        
        QPushButton:focus {{
            outline: 2px solid {scheme.get_color('primary')};
            outline-offset: 2px;
        }}
        
        /* 扁平按钮样式 */
        QPushButton[flat="true"] {{
            background-color: transparent;
            color: {scheme.get_color('primary')};
            border: 1px solid {scheme.get_color('outline')};
        }}
        
        QPushButton[flat="true"]:hover {{
            background-color: {scheme.get_color('surface_container_high')};
            border-color: {scheme.get_color('primary')};
        }}
        
        /* 危险按钮样式 */
        QPushButton[danger="true"] {{
            background-color: {scheme.get_color('error')};
            color: {scheme.get_color('on_primary')};
        }}
        
        QPushButton[danger="true"]:hover {{
            background-color: {scheme.get_color('error')};
            opacity: 0.8;
        }}
        
        /* 成功按钮样式 */
        QPushButton[success="true"] {{
            background-color: {scheme.get_color('success')};
            color: {scheme.get_color('on_primary')};
        }}
        
        QPushButton[success="true"]:hover {{
            background-color: {scheme.get_color('success')};
            opacity: 0.8;
        }}
        """
    
    def _generate_input_styles(self, scheme: ColorScheme) -> str:
        """生成输入框样式"""
        return f"""
        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {scheme.get_color('surface_container_highest')};
            color: {scheme.get_color('on_surface')};
            border: 1px solid {scheme.get_color('outline_variant')};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            selection-background-color: {scheme.get_color('primary_container')};
            selection-color: {scheme.get_color('on_primary_container')};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {scheme.get_color('primary')};
            border-width: 2px;
        }}
        
        QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {{
            background-color: {scheme.get_color('surface_variant')};
            color: {scheme.get_color('on_surface_variant')};
            border-color: {scheme.get_color('outline_variant')};
        }}
        
        /* 下拉框样式 */
        QComboBox {{
            background-color: {scheme.get_color('surface_container_highest')};
            color: {scheme.get_color('on_surface')};
            border: 1px solid {scheme.get_color('outline_variant')};
            border-radius: 8px;
            padding: 8px 12px;
            min-height: 24px;
        }}
        
        QComboBox:focus {{
            border-color: {scheme.get_color('primary')};
            border-width: 2px;
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {scheme.get_color('on_surface')};
            margin-right: 5px;
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {scheme.get_color('surface_container')};
            color: {scheme.get_color('on_surface')};
            border: 1px solid {scheme.get_color('outline_variant')};
            border-radius: 8px;
            selection-background-color: {scheme.get_color('primary_container')};
            selection-color: {scheme.get_color('on_primary_container')};
            outline: none;
        }}
        
        QComboBox QAbstractItemView::item {{
            padding: 8px 12px;
            border: none;
            min-height: 20px;
        }}
        
        QComboBox QAbstractItemView::item:selected {{
            background-color: {scheme.get_color('primary_container')};
            color: {scheme.get_color('on_primary_container')};
        }}
        
        QComboBox QAbstractItemView::item:hover {{
            background-color: {scheme.get_color('surface_container_high')};
        }}
        
        /* 复选框样式 */
        QCheckBox {{
            color: {scheme.get_color('on_surface')};
            spacing: 8px;
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border-radius: 3px;
            border: 2px solid {scheme.get_color('outline')};
            background-color: {scheme.get_color('surface')};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {scheme.get_color('primary')};
            border-color: {scheme.get_color('primary')};
        }}
        
        QCheckBox::indicator:hover {{
            border-color: {scheme.get_color('primary')};
        }}
        
        /* 单选按钮样式 */
        QRadioButton {{
            color: {scheme.get_color('on_surface')};
            spacing: 8px;
        }}
        
        QRadioButton::indicator {{
            width: 18px;
            height: 18px;
            border-radius: 9px;
            border: 2px solid {scheme.get_color('outline')};
            background-color: {scheme.get_color('surface')};
        }}
        
        QRadioButton::indicator:checked {{
            background-color: {scheme.get_color('primary')};
            border-color: {scheme.get_color('primary')};
        }}
        
        QRadioButton::indicator:hover {{
            border-color: {scheme.get_color('primary')};
        }}
        """
    
    def _generate_list_styles(self, scheme: ColorScheme) -> str:
        """生成列表样式"""
        return f"""
        /* 列表样式 */
        QListWidget, QTreeWidget {{
            background-color: {scheme.get_color('surface')};
            color: {scheme.get_color('on_surface')};
            border: 1px solid {scheme.get_color('outline_variant')};
            border-radius: 8px;
            outline: none;
            selection-background-color: {scheme.get_color('primary_container')};
            selection-color: {scheme.get_color('on_primary_container')};
        }}
        
        QListWidget::item, QTreeWidget::item {{
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            margin: 2px;
        }}
        
        QListWidget::item:hover, QTreeWidget::item:hover {{
            background-color: {scheme.get_color('surface_container_high')};
        }}
        
        QListWidget::item:selected, QTreeWidget::item:selected {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                       stop: 0 {scheme.get_color('primary')}, 
                                       stop: 1 {scheme.get_color('primary_container')});
            color: {scheme.get_color('on_primary')};
            border: 2px solid {scheme.get_color('primary')};
            border-radius: 8px;
            font-weight: bold;
        }}
        
        QListWidget::item:selected:hover, QTreeWidget::item:selected:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                       stop: 0 {scheme.get_color('primary_container')},
                                       stop: 1 {scheme.get_color('primary')});
            border: 2px solid {scheme.get_color('secondary')};
        }}
        """

    def _generate_table_styles(self, scheme: ColorScheme) -> str:
        """生成表格样式"""
        return f"""
        /* 表格样式 */
        QTableWidget {{
            background-color: {scheme.get_color('surface')};
            color: {scheme.get_color('on_surface')};
            border: 1px solid {scheme.get_color('outline_variant')};
            border-radius: 8px;
            gridline-color: {scheme.get_color('outline_variant')};
            selection-background-color: {scheme.get_color('primary_container')};
            selection-color: {scheme.get_color('on_primary_container')};
        }}

        QTableWidget::item {{
            padding: 8px 12px;
            border: none;
        }}

        QTableWidget::item:hover {{
            background-color: {scheme.get_color('surface_container_high')};
        }}

        QTableWidget::item:selected {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                       stop: 0 {scheme.get_color('primary_container')},
                                       stop: 1 {scheme.get_color('surface_container_high')});
            color: {scheme.get_color('on_primary_container')};
            border: 1px solid {scheme.get_color('primary')};
            border-radius: 4px;
            font-weight: bold;
        }}

        QHeaderView::section {{
            background-color: {scheme.get_color('surface_container')};
            color: {scheme.get_color('on_surface')};
            border: none;
            border-bottom: 1px solid {scheme.get_color('outline_variant')};
            padding: 8px 12px;
            font-weight: 500;
        }}
        """

    def _generate_tab_styles(self, scheme: ColorScheme) -> str:
        """生成标签页样式"""
        return f"""
        /* 标签页样式 */
        QTabWidget::pane {{
            background-color: {scheme.get_color('surface')};
            border: 1px solid {scheme.get_color('outline_variant')};
            border-radius: 8px;
            margin-top: -1px;
        }}

        QTabBar::tab {{
            background-color: {scheme.get_color('surface_container')};
            color: {scheme.get_color('on_surface')};
            border: 1px solid {scheme.get_color('outline_variant')};
            border-bottom: none;
            border-radius: 8px 8px 0 0;
            padding: 8px 16px;
            margin-right: 2px;
            min-width: 80px;
        }}

        QTabBar::tab:selected {{
            background-color: {scheme.get_color('primary')};
            color: {scheme.get_color('on_primary')};
            border-color: {scheme.get_color('primary')};
            font-weight: bold;
        }}

        QTabBar::tab:hover {{
            background-color: {scheme.get_color('surface_container_high')};
        }}

        QTabBar::tab:selected:hover {{
            background-color: {scheme.get_color('primary_container')};
            color: {scheme.get_color('on_primary_container')};
        }}
        """

    def _generate_scrollbar_styles(self, scheme: ColorScheme) -> str:
        """生成滚动条样式"""
        return f"""
        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {scheme.get_color('surface_container')};
            width: 12px;
            border-radius: 6px;
            margin: 0;
        }}

        QScrollBar::handle:vertical {{
            background-color: {scheme.get_color('outline')};
            border-radius: 6px;
            min-height: 20px;
            margin: 2px;
        }}

        QScrollBar::handle:vertical:hover {{
            background-color: {scheme.get_color('primary')};
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
            height: 0;
        }}

        QScrollBar:horizontal {{
            background-color: {scheme.get_color('surface_container')};
            height: 12px;
            border-radius: 6px;
            margin: 0;
        }}

        QScrollBar::handle:horizontal {{
            background-color: {scheme.get_color('outline')};
            border-radius: 6px;
            min-width: 20px;
            margin: 2px;
        }}

        QScrollBar::handle:horizontal:hover {{
            background-color: {scheme.get_color('primary')};
        }}

        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            border: none;
            background: none;
            width: 0;
        }}
        """

    def _generate_menu_styles(self, scheme: ColorScheme) -> str:
        """生成菜单样式"""
        return f"""
        /* 菜单样式 */
        QMenuBar {{
            background-color: {scheme.get_color('surface_container')};
            color: {scheme.get_color('on_surface')};
            border-bottom: 1px solid {scheme.get_color('outline_variant')};
        }}

        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 12px;
            border-radius: 4px;
        }}

        QMenuBar::item:selected {{
            background-color: {scheme.get_color('surface_container_high')};
        }}

        QMenuBar::item:pressed {{
            background-color: {scheme.get_color('primary_container')};
            color: {scheme.get_color('on_primary_container')};
        }}

        QMenu {{
            background-color: {scheme.get_color('surface_container')};
            color: {scheme.get_color('on_surface')};
            border: 1px solid {scheme.get_color('outline_variant')};
            border-radius: 8px;
            padding: 4px;
        }}

        QMenu::item {{
            background-color: transparent;
            padding: 8px 16px;
            border-radius: 4px;
        }}

        QMenu::item:selected {{
            background-color: {scheme.get_color('primary_container')};
            color: {scheme.get_color('on_primary_container')};
        }}

        QMenu::separator {{
            height: 1px;
            background-color: {scheme.get_color('outline_variant')};
            margin: 4px 0px;
        }}
        """

    def _generate_dialog_styles(self, scheme: ColorScheme) -> str:
        """生成对话框样式"""
        return f"""
        /* 对话框样式 */
        QDialog {{
            background-color: {scheme.get_color('surface')};
            color: {scheme.get_color('on_surface')};
        }}

        QMessageBox {{
            background-color: {scheme.get_color('surface')};
            color: {scheme.get_color('on_surface')};
        }}

        QMessageBox QPushButton {{
            min-width: 80px;
            padding: 8px 16px;
        }}

        /* 工具提示样式 */
        QToolTip {{
            background-color: {scheme.get_color('surface_container_highest')};
            color: {scheme.get_color('on_surface')};
            border: 1px solid {scheme.get_color('outline')};
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
        }}
        """

    def _generate_progress_styles(self, scheme: ColorScheme) -> str:
        """生成进度条样式"""
        return f"""
        /* 进度条样式 */
        QProgressBar {{
            background-color: {scheme.get_color('surface_container')};
            border: 1px solid {scheme.get_color('outline_variant')};
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            color: {scheme.get_color('on_surface')};
        }}

        QProgressBar::chunk {{
            background-color: {scheme.get_color('primary')};
            border-radius: 6px;
            margin: 1px;
        }}

        /* 滑块样式 */
        QSlider::groove:horizontal {{
            background-color: {scheme.get_color('surface_container')};
            height: 6px;
            border-radius: 3px;
        }}

        QSlider::handle:horizontal {{
            background-color: {scheme.get_color('primary')};
            border: 2px solid {scheme.get_color('on_primary')};
            width: 18px;
            height: 18px;
            border-radius: 9px;
            margin: -6px 0;
        }}

        QSlider::handle:horizontal:hover {{
            background-color: {scheme.get_color('primary_container')};
            border-color: {scheme.get_color('primary')};
        }}

        QSlider::sub-page:horizontal {{
            background-color: {scheme.get_color('primary')};
            border-radius: 3px;
        }}

        QSlider::add-page:horizontal {{
            background-color: {scheme.get_color('outline_variant')};
            border-radius: 3px;
        }}

        /* 垂直滑块 */
        QSlider::groove:vertical {{
            background-color: {scheme.get_color('surface_container')};
            width: 6px;
            border-radius: 3px;
        }}

        QSlider::handle:vertical {{
            background-color: {scheme.get_color('primary')};
            border: 2px solid {scheme.get_color('on_primary')};
            width: 18px;
            height: 18px;
            border-radius: 9px;
            margin: 0 -6px;
        }}

        QSlider::handle:vertical:hover {{
            background-color: {scheme.get_color('primary_container')};
            border-color: {scheme.get_color('primary')};
        }}

        QSlider::sub-page:vertical {{
            background-color: {scheme.get_color('outline_variant')};
            border-radius: 3px;
        }}

        QSlider::add-page:vertical {{
            background-color: {scheme.get_color('primary')};
            border-radius: 3px;
        }}
        """
