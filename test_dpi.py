#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DPI适配测试程序
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.dpi_adapter import setup_dpi_awareness, apply_dpi_scaling, dpi_adapter


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DPI适配测试")
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加测试控件
        title_label = QLabel("DPI适配测试")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(title_label)
        
        info_label = QLabel("这是一个测试界面，用于验证DPI适配功能")
        layout.addWidget(info_label)
        
        # DPI信息
        app = QApplication.instance()
        if app:
            screen = app.primaryScreen()
            if screen:
                dpi = screen.logicalDotsPerInch()
                physical_dpi = screen.physicalDotsPerInch()
                device_ratio = screen.devicePixelRatio()
                
                dpi_info = f"""
DPI信息:
- 逻辑DPI: {dpi}
- 物理DPI: {physical_dpi}
- 设备像素比: {device_ratio}
- 缩放因子: {dpi_adapter.scale_factor:.2f}
- 当前字体大小: {dpi_adapter.current_font_size}pt
                """
                
                dpi_label = QLabel(dpi_info)
                dpi_label.setStyleSheet("background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;")
                layout.addWidget(dpi_label)
        
        # 测试按钮
        test_button = QPushButton("测试按钮")
        test_button.clicked.connect(self.on_test_button_clicked)
        layout.addWidget(test_button)
        
        # 文本编辑器
        text_edit = QTextEdit()
        text_edit.setPlainText("这是一个文本编辑器，用于测试字体大小和界面缩放效果。\n\n"
                              "如果DPI适配正常工作，这些文字应该在不同分辨率的显示器上都能清晰可读。")
        layout.addWidget(text_edit)
        
        # 应用DPI适配
        dpi_adapter.apply_widget_scaling(self)
        
        # 设置窗口大小
        width, height = dpi_adapter.get_recommended_window_size(600, 400)
        self.resize(width, height)
        
    def on_test_button_clicked(self):
        """测试按钮点击"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "测试", "DPI适配功能正常工作！")


def main():
    """主函数"""
    # 设置DPI感知
    setup_dpi_awareness()
    
    # 设置Qt属性
    try:
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)
    except AttributeError:
        pass
    
    app = QApplication(sys.argv)
    
    # 应用DPI缩放
    apply_dpi_scaling(app)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print(f"DPI适配测试窗口已启动")
    print(f"窗口大小: {window.width()}x{window.height()}")
    print(f"缩放因子: {dpi_adapter.scale_factor:.2f}")
    print(f"字体大小: {dpi_adapter.current_font_size}pt")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
