"""
信息面板组件
显示项目状态、系统状态、进度信息等
"""

import os
import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QProgressBar, QFrame, QScrollArea)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPainter, QBrush, QColor

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.utils.logger import logger


class InfoPanel(QWidget):
    """信息面板组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.project_manager = None
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """初始化界面"""
        self.setFixedWidth(280)
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-left: 1px solid #e9ecef;
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f1f3f4;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #c1c8cd;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a8b1b8;
            }
        """)
        
        # 内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # 添加各个信息区块
        self.create_project_info_section(content_layout)
        self.create_help_section(content_layout)
        self.create_system_status_section(content_layout)
        self.create_progress_section(content_layout)
        
        # 添加弹性空间
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
    def create_section_header(self, title, icon=""):
        """创建区块标题"""
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # 图标和标题
        title_label = QLabel(f"{icon} {title}")
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        title_label.setStyleSheet("color: #495057; margin-bottom: 5px;")
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        return header_widget
        
    def create_info_item(self, label, value="", color="#6c757d"):
        """创建信息项"""
        item_widget = QWidget()
        item_layout = QVBoxLayout(item_widget)
        item_layout.setContentsMargins(0, 0, 0, 0)
        item_layout.setSpacing(2)
        
        # 标签
        label_widget = QLabel(label)
        label_widget.setFont(QFont("Microsoft YaHei", 8))
        label_widget.setStyleSheet("color: #6c757d;")
        
        # 值
        value_widget = QLabel(value)
        value_widget.setFont(QFont("Microsoft YaHei", 9))
        value_widget.setStyleSheet(f"color: {color}; font-weight: 500;")
        value_widget.setWordWrap(True)
        
        item_layout.addWidget(label_widget)
        item_layout.addWidget(value_widget)
        
        # 存储值标签以便更新
        item_widget.value_label = value_widget
        
        return item_widget
        
    def create_project_info_section(self, parent_layout):
        """创建项目信息区块"""
        # 标题
        header = self.create_section_header("项目信息", "📁")
        parent_layout.addWidget(header)
        
        # 项目信息容器
        self.project_info_container = QWidget()
        project_layout = QVBoxLayout(self.project_info_container)
        project_layout.setContentsMargins(10, 10, 10, 10)
        project_layout.setSpacing(12)
        
        # 设置容器样式
        self.project_info_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        # 项目名称
        self.project_name_item = self.create_info_item("项目名称", "未选择项目")
        project_layout.addWidget(self.project_name_item)
        
        # 创建时间
        self.created_time_item = self.create_info_item("创建时间", "-")
        project_layout.addWidget(self.created_time_item)
        
        # 最后修改
        self.modified_time_item = self.create_info_item("最后修改", "-")
        project_layout.addWidget(self.modified_time_item)
        
        parent_layout.addWidget(self.project_info_container)
        
    def create_help_section(self, parent_layout):
        """创建帮助区块"""
        # 标题
        header = self.create_section_header("帮助", "💡")
        parent_layout.addWidget(header)
        
        # 帮助容器
        help_container = QWidget()
        help_layout = QVBoxLayout(help_container)
        help_layout.setContentsMargins(10, 10, 10, 10)
        help_layout.setSpacing(8)
        
        help_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        # 帮助项目
        help_items = [
            ("🔗", "官网"),
            ("📖", "文档"),
            ("🎯", "教程"),
            ("💬", "配置参数"),
            ("📞", "联系客服")
        ]
        
        for icon, text in help_items:
            help_item = QLabel(f"{icon} {text}")
            help_item.setFont(QFont("Microsoft YaHei", 9))
            help_item.setStyleSheet("""
                QLabel {
                    color: #495057;
                    padding: 4px 0px;
                    border-radius: 4px;
                }
                QLabel:hover {
                    background-color: #f8f9fa;
                    color: #007bff;
                }
            """)
            help_item.setCursor(Qt.PointingHandCursor)
            help_layout.addWidget(help_item)
        
        parent_layout.addWidget(help_container)
        
    def create_system_status_section(self, parent_layout):
        """创建系统状态区块"""
        # 标题
        header = self.create_section_header("系统状态", "⚙️")
        parent_layout.addWidget(header)
        
        # 状态容器
        status_container = QWidget()
        status_layout = QVBoxLayout(status_container)
        status_layout.setContentsMargins(10, 10, 10, 10)
        status_layout.setSpacing(12)
        
        status_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        # CPU状态
        self.cpu_item = self.create_info_item("CPU", "●", "#28a745")
        status_layout.addWidget(self.cpu_item)
        
        # 内存状态
        self.memory_item = self.create_info_item("内存", "●", "#ffc107")
        status_layout.addWidget(self.memory_item)
        
        # 网络状态
        self.network_item = self.create_info_item("网络", "●", "#28a745")
        status_layout.addWidget(self.network_item)
        
        parent_layout.addWidget(status_container)
        
    def create_progress_section(self, parent_layout):
        """创建进度区块"""
        # 标题
        header = self.create_section_header("整体进度", "📊")
        parent_layout.addWidget(header)
        
        # 进度容器
        progress_container = QWidget()
        progress_layout = QVBoxLayout(progress_container)
        progress_layout.setContentsMargins(10, 10, 10, 10)
        progress_layout.setSpacing(12)
        
        progress_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        # 进度项目
        progress_items = [
            ("文本创作", 85),
            ("图像生成", 60),
            ("音频合成", 30),
            ("视频制作", 10)
        ]
        
        for name, value in progress_items:
            progress_item = self.create_progress_item(name, value)
            progress_layout.addWidget(progress_item)
        
        parent_layout.addWidget(progress_container)
        
    def create_progress_item(self, name, value):
        """创建进度项"""
        item_widget = QWidget()
        item_layout = QVBoxLayout(item_widget)
        item_layout.setContentsMargins(0, 0, 0, 0)
        item_layout.setSpacing(4)
        
        # 名称和百分比
        header_layout = QHBoxLayout()
        name_label = QLabel(name)
        name_label.setFont(QFont("Microsoft YaHei", 8))
        name_label.setStyleSheet("color: #495057;")
        
        percent_label = QLabel(f"{value}%")
        percent_label.setFont(QFont("Microsoft YaHei", 8))
        percent_label.setStyleSheet("color: #6c757d;")
        
        header_layout.addWidget(name_label)
        header_layout.addStretch()
        header_layout.addWidget(percent_label)
        
        # 进度条
        progress_bar = QProgressBar()
        progress_bar.setMaximum(100)
        progress_bar.setValue(value)
        progress_bar.setFixedHeight(6)
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                background-color: #e9ecef;
                border-radius: 3px;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 3px;
            }
        """)
        
        item_layout.addLayout(header_layout)
        item_layout.addWidget(progress_bar)
        
        # 存储进度条和百分比标签以便更新
        item_widget.progress_bar = progress_bar
        item_widget.percent_label = percent_label
        
        return item_widget
        
    def setup_timer(self):
        """设置定时器更新状态"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(5000)  # 每5秒更新一次
        
    def update_status(self):
        """更新状态信息"""
        try:
            # 更新项目信息
            self.update_project_info()
            
            # 更新系统状态
            self.update_system_status()
            
        except Exception as e:
            logger.error(f"更新信息面板状态失败: {e}")
            
    def update_project_info(self):
        """更新项目信息"""
        try:
            if self.project_manager and self.project_manager.current_project:
                project = self.project_manager.current_project
                
                # 项目名称
                project_name = project.get('project_name', '未命名项目')
                self.project_name_item.value_label.setText(project_name)
                
                # 创建时间
                created_time = project.get('created_time', '-')
                if created_time and created_time != '-':
                    # 格式化时间显示
                    try:
                        from datetime import datetime
                        if isinstance(created_time, str):
                            dt = datetime.fromisoformat(created_time.replace('Z', '+00:00'))
                            formatted_time = dt.strftime('%Y-%m-%d %H:%M')
                        else:
                            formatted_time = str(created_time)
                        self.created_time_item.value_label.setText(formatted_time)
                    except:
                        self.created_time_item.value_label.setText(created_time)
                
                # 最后修改时间
                modified_time = project.get('last_modified', '-')
                if modified_time and modified_time != '-':
                    try:
                        from datetime import datetime
                        if isinstance(modified_time, str):
                            dt = datetime.fromisoformat(modified_time.replace('Z', '+00:00'))
                            formatted_time = dt.strftime('%Y-%m-%d %H:%M')
                        else:
                            formatted_time = str(modified_time)
                        self.modified_time_item.value_label.setText(formatted_time)
                    except:
                        self.modified_time_item.value_label.setText(modified_time)
            else:
                # 没有项目时显示默认信息
                self.project_name_item.value_label.setText("未选择项目")
                self.created_time_item.value_label.setText("-")
                self.modified_time_item.value_label.setText("-")
                
        except Exception as e:
            logger.error(f"更新项目信息失败: {e}")
            
    def update_system_status(self):
        """更新系统状态"""
        try:
            # 这里可以添加实际的系统状态检测
            # 目前使用模拟数据
            pass
            
        except Exception as e:
            logger.error(f"更新系统状态失败: {e}")
            
    def set_project_manager(self, project_manager):
        """设置项目管理器"""
        self.project_manager = project_manager
        self.update_project_info()
