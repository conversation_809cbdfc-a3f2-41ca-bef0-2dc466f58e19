#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化工作流程测试脚本
模拟用户从创建项目到AI创作、五阶段分镜、配音生图的完整流程
"""

import sys
import os
import asyncio
import time
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_service_manager():
    """测试服务管理器初始化"""
    print("🔧 测试1: 服务管理器初始化")
    try:
        from src.core.service_manager import ServiceManager, ServiceType
        from src.utils.config_manager import ConfigManager
        
        print("  ✓ 导入服务管理器模块成功")
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        print("  ✓ 配置管理器初始化成功")
        
        # 初始化服务管理器
        service_manager = ServiceManager(config_manager)
        print("  ✓ 服务管理器初始化成功")
        
        # 测试LLM服务
        llm_service = service_manager.get_service(ServiceType.LLM)
        if llm_service:
            print("  ✓ LLM服务获取成功")
        else:
            print("  ❌ LLM服务获取失败")
            return False
            
        # 测试图像服务
        image_service = service_manager.get_service(ServiceType.IMAGE)
        if image_service:
            print("  ✓ 图像服务获取成功")
        else:
            print("  ⚠️ 图像服务获取失败")
            
        # 测试语音服务
        voice_service = service_manager.get_service(ServiceType.VOICE)
        if voice_service:
            print("  ✓ 语音服务获取成功")
        else:
            print("  ⚠️ 语音服务获取失败")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 服务管理器测试失败: {e}")
        traceback.print_exc()
        return False

async def test_ai_story_creation():
    """测试AI故事创作"""
    print("\n📝 测试2: AI故事创作")
    try:
        from src.core.service_manager import ServiceManager, ServiceType
        
        service_manager = ServiceManager()
        llm_service = service_manager.get_service(ServiceType.LLM)
        
        if not llm_service:
            print("  ❌ LLM服务未找到")
            return False
            
        print("  ✓ LLM服务获取成功")
        
        # 测试故事创作
        theme = "一只勇敢的小猫咪的冒险故事"
        print(f"  📖 创作主题: {theme}")
        
        result = await llm_service.create_story_from_theme(theme)
        
        if result.success:
            story_content = result.data.get('content', '')
            print(f"  ✓ AI故事创作成功，内容长度: {len(story_content)} 字符")
            print(f"  📄 故事预览: {story_content[:100]}...")
            return story_content
        else:
            print(f"  ❌ AI故事创作失败: {result.error}")
            return False
            
    except Exception as e:
        print(f"  ❌ AI故事创作测试失败: {e}")
        traceback.print_exc()
        return False

async def test_ai_text_rewrite():
    """测试AI文本改写"""
    print("\n✏️ 测试3: AI文本改写")
    try:
        from src.core.service_manager import ServiceManager, ServiceType
        
        service_manager = ServiceManager()
        llm_service = service_manager.get_service(ServiceType.LLM)
        
        if not llm_service:
            print("  ❌ LLM服务未找到")
            return False
            
        # 测试文本改写
        original_text = "小猫咪很可爱，它喜欢玩球。"
        print(f"  📝 原始文本: {original_text}")
        
        result = await llm_service.rewrite_text(original_text)
        
        if result.success:
            rewritten_content = result.data.get('content', '')
            print(f"  ✓ AI文本改写成功")
            print(f"  📄 改写结果: {rewritten_content}")
            return rewritten_content
        else:
            print(f"  ❌ AI文本改写失败: {result.error}")
            return False
            
    except Exception as e:
        print(f"  ❌ AI文本改写测试失败: {e}")
        traceback.print_exc()
        return False

def test_project_creation():
    """测试项目创建"""
    print("\n📁 测试4: 项目创建")
    try:
        from src.core.project_manager import ProjectManager
        
        project_manager = ProjectManager()
        print("  ✓ 项目管理器初始化成功")
        
        # 创建测试项目
        project_name = "测试项目_AI工作流程"
        project_path = f"./test_projects/{project_name}"
        
        # 确保测试目录存在
        os.makedirs("./test_projects", exist_ok=True)
        
        # 模拟项目创建
        print(f"  📂 创建项目: {project_name}")
        print(f"  📍 项目路径: {project_path}")
        print("  ✓ 项目创建成功")
        
        return project_path
        
    except Exception as e:
        print(f"  ❌ 项目创建测试失败: {e}")
        traceback.print_exc()
        return False

def test_five_stage_storyboard():
    """测试五阶段分镜系统"""
    print("\n🎬 测试5: 五阶段分镜系统")
    try:
        # 检查五阶段分镜标签页是否可以导入
        from src.gui.five_stage_storyboard_tab import FiveStageStoryboardTab
        print("  ✓ 五阶段分镜标签页导入成功")
        
        # 检查是否有LLMApi的遗留引用
        import inspect
        source = inspect.getsource(FiveStageStoryboardTab)
        
        if "self.llm_api._make_api_call" in source:
            print("  ⚠️ 发现旧的LLMApi调用，需要修复")
            return "需要修复"
        else:
            print("  ✓ 没有发现旧的LLMApi调用")
            
        print("  ✓ 五阶段分镜系统检查完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 五阶段分镜系统测试失败: {e}")
        traceback.print_exc()
        return False

def test_voice_generation():
    """测试语音生成"""
    print("\n🎤 测试6: 语音生成")
    try:
        from src.gui.voice_generation_tab import VoiceGenerationTab
        print("  ✓ 语音生成标签页导入成功")
        
        # 检查语音服务
        from src.core.service_manager import ServiceManager, ServiceType
        service_manager = ServiceManager()
        voice_service = service_manager.get_service(ServiceType.VOICE)
        
        if voice_service:
            print("  ✓ 语音服务可用")
        else:
            print("  ⚠️ 语音服务不可用")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 语音生成测试失败: {e}")
        traceback.print_exc()
        return False

def test_image_generation():
    """测试图像生成"""
    print("\n🖼️ 测试7: 图像生成")
    try:
        from src.gui.storyboard_image_generation_tab import StoryboardImageGenerationTab
        print("  ✓ 图像生成标签页导入成功")
        
        # 检查图像服务
        from src.core.service_manager import ServiceManager, ServiceType
        service_manager = ServiceManager()
        image_service = service_manager.get_service(ServiceType.IMAGE)
        
        if image_service:
            print("  ✓ 图像服务可用")
        else:
            print("  ⚠️ 图像服务不可用")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 图像生成测试失败: {e}")
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始自动化工作流程测试")
    print("=" * 60)
    
    results = {}
    
    # 测试1: 服务管理器
    results['service_manager'] = test_service_manager()
    
    # 测试2: AI故事创作
    if results['service_manager']:
        results['ai_story'] = await test_ai_story_creation()
    else:
        print("\n📝 跳过AI故事创作测试（服务管理器失败）")
        results['ai_story'] = False
    
    # 测试3: AI文本改写
    if results['service_manager']:
        results['ai_rewrite'] = await test_ai_text_rewrite()
    else:
        print("\n✏️ 跳过AI文本改写测试（服务管理器失败）")
        results['ai_rewrite'] = False
    
    # 测试4: 项目创建
    results['project'] = test_project_creation()
    
    # 测试5: 五阶段分镜
    results['storyboard'] = test_five_stage_storyboard()
    
    # 测试6: 语音生成
    results['voice'] = test_voice_generation()
    
    # 测试7: 图像生成
    results['image'] = test_image_generation()
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    for test_name, result in results.items():
        if result is True:
            status = "✅ 通过"
        elif result == "需要修复":
            status = "⚠️ 需要修复"
        elif result is False:
            status = "❌ 失败"
        else:
            status = "✅ 部分成功"
            
        print(f"  {test_name}: {status}")
    
    # 检查关键问题
    critical_issues = []
    if not results['service_manager']:
        critical_issues.append("服务管理器初始化失败")
    if results['storyboard'] == "需要修复":
        critical_issues.append("五阶段分镜系统有旧代码")
    if not results['ai_story'] and not results['ai_rewrite']:
        critical_issues.append("AI功能完全不可用")
    
    if critical_issues:
        print(f"\n🚨 发现关键问题:")
        for issue in critical_issues:
            print(f"  - {issue}")
    else:
        print(f"\n🎉 工作流程基本正常！")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
