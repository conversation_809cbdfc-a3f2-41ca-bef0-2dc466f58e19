#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化配色方案
提供多种Material Design和Fluent Design风格的配色方案
支持浅色和深色主题，优化高DPI显示效果
"""

from enum import Enum
from typing import Dict, Any
from dataclasses import dataclass


class ColorSchemeType(Enum):
    """配色方案类型"""
    OCEAN_BLUE = "ocean_blue"
    FOREST_GREEN = "forest_green"
    VIOLET = "violet"
    WARM_ORANGE = "warm_orange"
    GRAPHITE = "graphite"


class ThemeMode(Enum):
    """主题模式"""
    LIGHT = "light"
    DARK = "dark"


@dataclass
class ColorScheme:
    """配色方案数据类"""
    name: str
    mode: ThemeMode
    colors: Dict[str, str]
    
    def get_color(self, key: str, fallback: str = "#000000") -> str:
        """获取颜色值"""
        return self.colors.get(key, fallback)


class ColorSchemes:
    """配色方案管理器"""
    
    # 深海蓝配色方案
    OCEAN_BLUE_LIGHT = ColorScheme(
        name="深海蓝 浅色",
        mode=ThemeMode.LIGHT,
        colors={
            # 主色系
            "primary": "#0077BE",
            "primary_container": "#B3E5FC",
            "on_primary": "#FFFFFF",
            "on_primary_container": "#001F2A",
            
            # 次要色系
            "secondary": "#00ACC1",
            "secondary_container": "#B2EBF2",
            "on_secondary": "#FFFFFF",
            "on_secondary_container": "#002021",
            
            # 表面色系
            "surface": "#FAFAFA",
            "surface_container": "#F5F5F5",
            "surface_container_high": "#EEEEEE",
            "surface_container_highest": "#E0E0E0",
            "on_surface": "#1A1A1A",
            "on_surface_variant": "#424242",
            
            # 背景色系
            "background": "#FFFFFF",
            "on_background": "#1A1A1A",
            
            # 轮廓色
            "outline": "#757575",
            "outline_variant": "#BDBDBD",
            
            # 状态色
            "success": "#4CAF50",
            "warning": "#FF9800",
            "error": "#F44336",
            "info": "#2196F3",
            
            # 特殊色
            "shadow": "rgba(0, 0, 0, 0.1)",
            "scrim": "rgba(0, 0, 0, 0.32)",
        }
    )
    
    OCEAN_BLUE_DARK = ColorScheme(
        name="深海蓝 深色",
        mode=ThemeMode.DARK,
        colors={
            # 主色系
            "primary": "#4FC3F7",
            "primary_container": "#0277BD",
            "on_primary": "#001F2A",
            "on_primary_container": "#B3E5FC",
            
            # 次要色系
            "secondary": "#4DD0E1",
            "secondary_container": "#00838F",
            "on_secondary": "#002021",
            "on_secondary_container": "#B2EBF2",
            
            # 表面色系
            "surface": "#121212",
            "surface_container": "#1E1E1E",
            "surface_container_high": "#2C2C2C",
            "surface_container_highest": "#363636",
            "on_surface": "#E0E0E0",
            "on_surface_variant": "#BDBDBD",
            
            # 背景色系
            "background": "#0A0A0A",
            "on_background": "#E0E0E0",
            
            # 轮廓色
            "outline": "#757575",
            "outline_variant": "#424242",
            
            # 状态色
            "success": "#66BB6A",
            "warning": "#FFB74D",
            "error": "#EF5350",
            "info": "#42A5F5",
            
            # 特殊色
            "shadow": "rgba(0, 0, 0, 0.3)",
            "scrim": "rgba(0, 0, 0, 0.6)",
        }
    )
    
    # 森林绿配色方案
    FOREST_GREEN_LIGHT = ColorScheme(
        name="森林绿 浅色",
        mode=ThemeMode.LIGHT,
        colors={
            # 主色系
            "primary": "#2E7D32",
            "primary_container": "#C8E6C9",
            "on_primary": "#FFFFFF",
            "on_primary_container": "#1B5E20",
            
            # 次要色系
            "secondary": "#66BB6A",
            "secondary_container": "#E8F5E8",
            "on_secondary": "#FFFFFF",
            "on_secondary_container": "#2E7D32",
            
            # 表面色系
            "surface": "#F1F8E9",
            "surface_container": "#E8F5E8",
            "surface_container_high": "#DCEDC8",
            "surface_container_highest": "#C5E1A5",
            "on_surface": "#1B5E20",
            "on_surface_variant": "#388E3C",
            
            # 背景色系
            "background": "#FFFFFF",
            "on_background": "#1B5E20",
            
            # 轮廓色
            "outline": "#689F38",
            "outline_variant": "#AED581",
            
            # 状态色
            "success": "#4CAF50",
            "warning": "#FF9800",
            "error": "#F44336",
            "info": "#2196F3",
            
            # 特殊色
            "shadow": "rgba(46, 125, 50, 0.1)",
            "scrim": "rgba(27, 94, 32, 0.32)",
        }
    )
    
    FOREST_GREEN_DARK = ColorScheme(
        name="森林绿 深色",
        mode=ThemeMode.DARK,
        colors={
            # 主色系
            "primary": "#81C784",
            "primary_container": "#2E7D32",
            "on_primary": "#1B5E20",
            "on_primary_container": "#C8E6C9",
            
            # 次要色系
            "secondary": "#A5D6A7",
            "secondary_container": "#388E3C",
            "on_secondary": "#2E7D32",
            "on_secondary_container": "#E8F5E8",
            
            # 表面色系
            "surface": "#0D1F0D",
            "surface_container": "#1A2E1A",
            "surface_container_high": "#263D26",
            "surface_container_highest": "#334D33",
            "on_surface": "#C8E6C9",
            "on_surface_variant": "#A5D6A7",
            
            # 背景色系
            "background": "#0A1A0A",
            "on_background": "#C8E6C9",
            
            # 轮廓色
            "outline": "#689F38",
            "outline_variant": "#4CAF50",
            
            # 状态色
            "success": "#66BB6A",
            "warning": "#FFB74D",
            "error": "#EF5350",
            "info": "#42A5F5",
            
            # 特殊色
            "shadow": "rgba(0, 0, 0, 0.3)",
            "scrim": "rgba(0, 0, 0, 0.6)",
        }
    )
    
    # 紫罗兰配色方案
    VIOLET_LIGHT = ColorScheme(
        name="紫罗兰 浅色",
        mode=ThemeMode.LIGHT,
        colors={
            # 主色系
            "primary": "#7B1FA2",
            "primary_container": "#E1BEE7",
            "on_primary": "#FFFFFF",
            "on_primary_container": "#4A148C",
            
            # 次要色系
            "secondary": "#AB47BC",
            "secondary_container": "#F3E5F5",
            "on_secondary": "#FFFFFF",
            "on_secondary_container": "#6A1B9A",
            
            # 表面色系
            "surface": "#F3E5F5",
            "surface_container": "#E1BEE7",
            "surface_container_high": "#CE93D8",
            "surface_container_highest": "#BA68C8",
            "on_surface": "#4A148C",
            "on_surface_variant": "#6A1B9A",
            
            # 背景色系
            "background": "#FFFFFF",
            "on_background": "#4A148C",
            
            # 轮廓色
            "outline": "#9C27B0",
            "outline_variant": "#CE93D8",
            
            # 状态色
            "success": "#4CAF50",
            "warning": "#FF9800",
            "error": "#F44336",
            "info": "#2196F3",
            
            # 特殊色
            "shadow": "rgba(123, 31, 162, 0.1)",
            "scrim": "rgba(74, 20, 140, 0.32)",
        }
    )
    
    VIOLET_DARK = ColorScheme(
        name="紫罗兰 深色",
        mode=ThemeMode.DARK,
        colors={
            # 主色系
            "primary": "#CE93D8",
            "primary_container": "#7B1FA2",
            "on_primary": "#4A148C",
            "on_primary_container": "#E1BEE7",
            
            # 次要色系
            "secondary": "#E1BEE7",
            "secondary_container": "#8E24AA",
            "on_secondary": "#6A1B9A",
            "on_secondary_container": "#F3E5F5",
            
            # 表面色系
            "surface": "#1A0D1F",
            "surface_container": "#2E1A33",
            "surface_container_high": "#422647",
            "surface_container_highest": "#56335B",
            "on_surface": "#E1BEE7",
            "on_surface_variant": "#CE93D8",
            
            # 背景色系
            "background": "#140A1A",
            "on_background": "#E1BEE7",
            
            # 轮廓色
            "outline": "#9C27B0",
            "outline_variant": "#7B1FA2",
            
            # 状态色
            "success": "#66BB6A",
            "warning": "#FFB74D",
            "error": "#EF5350",
            "info": "#42A5F5",
            
            # 特殊色
            "shadow": "rgba(0, 0, 0, 0.3)",
            "scrim": "rgba(0, 0, 0, 0.6)",
        }
    )
    
    # 暖橙配色方案
    WARM_ORANGE_LIGHT = ColorScheme(
        name="暖橙 浅色",
        mode=ThemeMode.LIGHT,
        colors={
            # 主色系
            "primary": "#F57C00",
            "primary_container": "#FFE0B2",
            "on_primary": "#FFFFFF",
            "on_primary_container": "#E65100",

            # 次要色系
            "secondary": "#FF9800",
            "secondary_container": "#FFF3E0",
            "on_secondary": "#FFFFFF",
            "on_secondary_container": "#F57C00",

            # 表面色系
            "surface": "#FFF8E1",
            "surface_container": "#FFECB3",
            "surface_container_high": "#FFE082",
            "surface_container_highest": "#FFD54F",
            "on_surface": "#E65100",
            "on_surface_variant": "#F57C00",

            # 背景色系
            "background": "#FFFFFF",
            "on_background": "#E65100",

            # 轮廓色
            "outline": "#FF9800",
            "outline_variant": "#FFCC02",

            # 状态色
            "success": "#4CAF50",
            "warning": "#FF9800",
            "error": "#F44336",
            "info": "#2196F3",

            # 特殊色
            "shadow": "rgba(245, 124, 0, 0.1)",
            "scrim": "rgba(230, 81, 0, 0.32)",
        }
    )

    WARM_ORANGE_DARK = ColorScheme(
        name="暖橙 深色",
        mode=ThemeMode.DARK,
        colors={
            # 主色系
            "primary": "#FFB74D",
            "primary_container": "#F57C00",
            "on_primary": "#E65100",
            "on_primary_container": "#FFE0B2",

            # 次要色系
            "secondary": "#FFCC02",
            "secondary_container": "#FF8F00",
            "on_secondary": "#F57C00",
            "on_secondary_container": "#FFF3E0",

            # 表面色系
            "surface": "#1F1A0D",
            "surface_container": "#332E1A",
            "surface_container_high": "#474226",
            "surface_container_highest": "#5B5633",
            "on_surface": "#FFE0B2",
            "on_surface_variant": "#FFCC02",

            # 背景色系
            "background": "#1A140A",
            "on_background": "#FFE0B2",

            # 轮廓色
            "outline": "#FF9800",
            "outline_variant": "#F57C00",

            # 状态色
            "success": "#66BB6A",
            "warning": "#FFB74D",
            "error": "#EF5350",
            "info": "#42A5F5",

            # 特殊色
            "shadow": "rgba(0, 0, 0, 0.3)",
            "scrim": "rgba(0, 0, 0, 0.6)",
        }
    )

    # 石墨灰配色方案
    GRAPHITE_LIGHT = ColorScheme(
        name="石墨灰 浅色",
        mode=ThemeMode.LIGHT,
        colors={
            # 主色系
            "primary": "#455A64",
            "primary_container": "#CFD8DC",
            "on_primary": "#FFFFFF",
            "on_primary_container": "#263238",

            # 次要色系
            "secondary": "#607D8B",
            "secondary_container": "#ECEFF1",
            "on_secondary": "#FFFFFF",
            "on_secondary_container": "#37474F",

            # 表面色系
            "surface": "#ECEFF1",
            "surface_container": "#CFD8DC",
            "surface_container_high": "#B0BEC5",
            "surface_container_highest": "#90A4AE",
            "on_surface": "#263238",
            "on_surface_variant": "#37474F",

            # 背景色系
            "background": "#FFFFFF",
            "on_background": "#263238",

            # 轮廓色
            "outline": "#78909C",
            "outline_variant": "#B0BEC5",

            # 状态色
            "success": "#4CAF50",
            "warning": "#FF9800",
            "error": "#F44336",
            "info": "#2196F3",

            # 特殊色
            "shadow": "rgba(69, 90, 100, 0.1)",
            "scrim": "rgba(38, 50, 56, 0.32)",
        }
    )

    GRAPHITE_DARK = ColorScheme(
        name="石墨灰 深色",
        mode=ThemeMode.DARK,
        colors={
            # 主色系
            "primary": "#90A4AE",
            "primary_container": "#455A64",
            "on_primary": "#263238",
            "on_primary_container": "#CFD8DC",

            # 次要色系
            "secondary": "#B0BEC5",
            "secondary_container": "#546E7A",
            "on_secondary": "#37474F",
            "on_secondary_container": "#ECEFF1",

            # 表面色系
            "surface": "#0F1419",
            "surface_container": "#1C2328",
            "surface_container_high": "#293237",
            "surface_container_highest": "#364146",
            "on_surface": "#CFD8DC",
            "on_surface_variant": "#B0BEC5",

            # 背景色系
            "background": "#0A0E14",
            "on_background": "#CFD8DC",

            # 轮廓色
            "outline": "#78909C",
            "outline_variant": "#546E7A",

            # 状态色
            "success": "#66BB6A",
            "warning": "#FFB74D",
            "error": "#EF5350",
            "info": "#42A5F5",

            # 特殊色
            "shadow": "rgba(0, 0, 0, 0.3)",
            "scrim": "rgba(0, 0, 0, 0.6)",
        }
    )

    @classmethod
    def get_scheme(cls, scheme_type: ColorSchemeType, mode: ThemeMode) -> ColorScheme:
        """获取指定的配色方案"""
        scheme_map = {
            (ColorSchemeType.OCEAN_BLUE, ThemeMode.LIGHT): cls.OCEAN_BLUE_LIGHT,
            (ColorSchemeType.OCEAN_BLUE, ThemeMode.DARK): cls.OCEAN_BLUE_DARK,
            (ColorSchemeType.FOREST_GREEN, ThemeMode.LIGHT): cls.FOREST_GREEN_LIGHT,
            (ColorSchemeType.FOREST_GREEN, ThemeMode.DARK): cls.FOREST_GREEN_DARK,
            (ColorSchemeType.VIOLET, ThemeMode.LIGHT): cls.VIOLET_LIGHT,
            (ColorSchemeType.VIOLET, ThemeMode.DARK): cls.VIOLET_DARK,
            (ColorSchemeType.WARM_ORANGE, ThemeMode.LIGHT): cls.WARM_ORANGE_LIGHT,
            (ColorSchemeType.WARM_ORANGE, ThemeMode.DARK): cls.WARM_ORANGE_DARK,
            (ColorSchemeType.GRAPHITE, ThemeMode.LIGHT): cls.GRAPHITE_LIGHT,
            (ColorSchemeType.GRAPHITE, ThemeMode.DARK): cls.GRAPHITE_DARK,
        }

        return scheme_map.get((scheme_type, mode), cls.OCEAN_BLUE_LIGHT)
    
    @classmethod
    def get_all_schemes(cls) -> Dict[str, ColorScheme]:
        """获取所有配色方案"""
        return {
            "ocean_blue_light": cls.OCEAN_BLUE_LIGHT,
            "ocean_blue_dark": cls.OCEAN_BLUE_DARK,
            "forest_green_light": cls.FOREST_GREEN_LIGHT,
            "forest_green_dark": cls.FOREST_GREEN_DARK,
            "violet_light": cls.VIOLET_LIGHT,
            "violet_dark": cls.VIOLET_DARK,
            "warm_orange_light": cls.WARM_ORANGE_LIGHT,
            "warm_orange_dark": cls.WARM_ORANGE_DARK,
            "graphite_light": cls.GRAPHITE_LIGHT,
            "graphite_dark": cls.GRAPHITE_DARK,
        }

    @classmethod
    def get_scheme_names(cls) -> Dict[ColorSchemeType, str]:
        """获取配色方案的中文名称"""
        return {
            ColorSchemeType.OCEAN_BLUE: "深海蓝",
            ColorSchemeType.FOREST_GREEN: "森林绿",
            ColorSchemeType.VIOLET: "紫罗兰",
            ColorSchemeType.WARM_ORANGE: "暖橙",
            ColorSchemeType.GRAPHITE: "石墨灰",
        }
