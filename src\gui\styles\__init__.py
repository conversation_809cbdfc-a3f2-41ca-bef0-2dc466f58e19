#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一样式系统
整合所有样式相关功能，提供简洁的API接口
"""

# 导入核心组件
from .color_schemes import (
    ColorScheme, ColorSchemes, ColorSchemeType, ThemeMode
)

from .theme_manager import (
    Theme<PERSON><PERSON>ger, get_theme_manager,
    apply_theme_to_application, apply_theme_to_widget,
    set_color_scheme, set_theme_mode, toggle_theme_mode,
    get_current_color
)

from .style_generator import StyleGenerator

from .unified_style_applier import (
    UnifiedStyleApplier, get_style_applier,
    apply_modern_style, toggle_theme,
    # 主题切换函数
    switch_to_ocean_blue, switch_to_forest_green, switch_to_violet,
    switch_to_warm_orange, switch_to_graphite,
    switch_to_light_mode, switch_to_dark_mode,
    # 预设主题函数
    apply_business_theme, apply_nature_theme, apply_creative_theme,
    apply_energetic_theme, apply_professional_theme,
    # 信息获取函数
    get_theme_info, get_available_color_schemes,
    is_dark_mode, is_light_mode,
    # 样式刷新函数
    refresh_application_style, refresh_widget_style
)

# 版本信息
__version__ = "2.0.0"
__author__ = "AI Video Generator Team"

# 导出的公共API
__all__ = [
    # 核心类
    'ColorScheme', 'ColorSchemes', 'ColorSchemeType', 'ThemeMode',
    'ThemeManager', 'StyleGenerator', 'UnifiedStyleApplier',
    
    # 实例获取函数
    'get_theme_manager', 'get_style_applier',
    
    # 基础样式应用函数
    'apply_modern_style', 'apply_theme_to_application', 'apply_theme_to_widget',
    
    # 主题设置函数
    'set_color_scheme', 'set_theme_mode', 'toggle_theme', 'toggle_theme_mode',
    
    # 快速主题切换函数
    'switch_to_ocean_blue', 'switch_to_forest_green', 'switch_to_violet',
    'switch_to_warm_orange', 'switch_to_graphite',
    'switch_to_light_mode', 'switch_to_dark_mode',
    
    # 预设主题函数
    'apply_business_theme', 'apply_nature_theme', 'apply_creative_theme',
    'apply_energetic_theme', 'apply_professional_theme',
    
    # 信息获取函数
    'get_current_color', 'get_theme_info', 'get_available_color_schemes',
    'is_dark_mode', 'is_light_mode',
    
    # 样式刷新函数
    'refresh_application_style', 'refresh_widget_style',
]

# 便捷导入
def init_style_system():
    """初始化样式系统"""
    try:
        # 获取主题管理器实例（会自动初始化）
        theme_manager = get_theme_manager()
        
        # 获取样式应用器实例（会自动初始化）
        style_applier = get_style_applier()
        
        print(f"样式系统初始化完成 - 版本: {__version__}")
        print(f"当前主题: {theme_manager.get_current_scheme_info()['full_name']}")
        
        return True
    except Exception as e:
        print(f"样式系统初始化失败: {e}")
        return False


def get_system_info():
    """获取样式系统信息"""
    try:
        theme_manager = get_theme_manager()
        current_info = theme_manager.get_current_scheme_info()
        available_schemes = theme_manager.get_available_schemes()
        
        return {
            "version": __version__,
            "current_theme": current_info,
            "available_schemes": list(available_schemes.keys()),
            "total_schemes": len(available_schemes),
            "modes": ["light", "dark"]
        }
    except Exception as e:
        return {"error": str(e)}


# 快速配置函数
def quick_setup_light_theme():
    """快速设置浅色主题"""
    switch_to_ocean_blue()
    switch_to_light_mode()
    apply_modern_style()


def quick_setup_dark_theme():
    """快速设置深色主题"""
    switch_to_ocean_blue()
    switch_to_dark_mode()
    apply_modern_style()


def quick_setup_business_theme():
    """快速设置商务主题"""
    apply_business_theme()
    apply_modern_style()


def quick_setup_creative_theme():
    """快速设置创意主题"""
    apply_creative_theme()
    apply_modern_style()


# 主题预览函数
def preview_all_themes():
    """预览所有主题（返回主题信息列表）"""
    themes = []
    for scheme_type in ColorSchemeType:
        for mode in ThemeMode:
            scheme = ColorSchemes.get_scheme(scheme_type, mode)
            themes.append({
                "name": scheme.name,
                "type": scheme_type.value,
                "mode": mode.value,
                "primary_color": scheme.get_color('primary'),
                "background_color": scheme.get_color('background'),
                "surface_color": scheme.get_color('surface')
            })
    return themes


# 兼容性支持
def migrate_from_old_style_system():
    """从旧样式系统迁移"""
    print("正在迁移到新的统一样式系统...")
    
    try:
        # 初始化新系统
        init_style_system()
        
        # 应用默认主题
        quick_setup_light_theme()
        
        print("迁移完成！新样式系统已激活。")
        return True
    except Exception as e:
        print(f"迁移失败: {e}")
        return False


# 调试函数
def debug_style_system():
    """调试样式系统"""
    print("=== 样式系统调试信息 ===")
    
    try:
        # 系统信息
        info = get_system_info()
        print(f"版本: {info.get('version', 'Unknown')}")
        print(f"当前主题: {info.get('current_theme', {}).get('full_name', 'Unknown')}")
        print(f"可用方案数: {info.get('total_schemes', 0)}")
        
        # 主题管理器状态
        theme_manager = get_theme_manager()
        print(f"主题管理器: {'已初始化' if theme_manager else '未初始化'}")
        
        # 样式应用器状态
        style_applier = get_style_applier()
        print(f"样式应用器: {'已初始化' if style_applier else '未初始化'}")
        
        # 当前颜色示例
        print("当前颜色示例:")
        print(f"  主色: {get_current_color('primary')}")
        print(f"  背景色: {get_current_color('background')}")
        print(f"  表面色: {get_current_color('surface')}")
        
        print("=== 调试信息结束 ===")
        
    except Exception as e:
        print(f"调试过程中出错: {e}")


# 自动初始化（可选）
def auto_init():
    """自动初始化样式系统"""
    try:
        return init_style_system()
    except:
        return False


# 如果直接运行此模块，执行调试
if __name__ == "__main__":
    print("统一样式系统 - 调试模式")
    debug_style_system()
    
    print("\n可用的主题预览:")
    themes = preview_all_themes()
    for theme in themes[:5]:  # 只显示前5个
        print(f"  {theme['name']}: {theme['primary_color']}")
