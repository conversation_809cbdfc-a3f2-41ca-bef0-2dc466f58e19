#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化卡片式主窗口
基于用户提供的界面设计，采用左侧导航、中央卡片区域、右侧信息面板的布局
"""

import sys
import os
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QScrollArea, QSplitter, QProgressBar,
    QApplication, QStackedWidget, QListWidget, QListWidgetItem, QMessageBox, QDialog
)
import os
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap

from src.utils.logger import logger
from src.core.app_controller import AppController
from src.core.project_manager import ProjectManager
from .modern_card_styles import apply_modern_card_styles

# 导入现有的功能标签页
from .five_stage_storyboard_tab import FiveStageStoryboardTab
from .voice_generation_tab import VoiceGenerationTab
from .storyboard_image_generation_tab import StoryboardImageGenerationTab
from .video_generation_tab import VideoGenerationTab
from .settings_tab import SettingsTab
from .consistency_control_panel import ConsistencyControlPanel


class ModernCardButton(QPushButton):
    """现代化卡片式按钮"""
    
    def __init__(self, text, icon_text="", parent=None):
        super().__init__(parent)
        self.setText(text)
        self.icon_text = icon_text
        self.setup_style()
    
    def setup_style(self):
        """设置按钮样式"""
        self.setMinimumHeight(50)
        self.setMinimumWidth(160)
        self.setFont(QFont("Microsoft YaHei UI", 10, QFont.Medium))

        # 设置样式类
        self.setProperty("class", "modern-card-button")
        self.setCheckable(True)


class ModernCard(QFrame):
    """现代化卡片容器"""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.title = title
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """设置UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(20, 16, 20, 20)
        self.layout.setSpacing(12)
        
        # 标题
        if self.title:
            self.title_label = QLabel(self.title)
            self.title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Bold))
            self.title_label.setStyleSheet("color: #333333; margin-bottom: 8px;")
            self.layout.addWidget(self.title_label)
    
    def setup_style(self):
        """设置卡片样式"""
        self.setProperty("class", "modern-card")
    
    def add_widget(self, widget):
        """添加控件到卡片"""
        self.layout.addWidget(widget)
    
    def add_layout(self, layout):
        """添加布局到卡片"""
        self.layout.addLayout(layout)


class StatusCard(ModernCard):
    """状态显示卡片"""
    
    def __init__(self, parent=None):
        super().__init__("系统状态", parent)
        self.setup_status_ui()
    
    def setup_status_ui(self):
        """设置状态UI"""
        # GPU状态
        gpu_layout = QHBoxLayout()
        gpu_label = QLabel("GPU:")
        gpu_label.setProperty("class", "status-label")
        gpu_status = QLabel("●")
        gpu_status.setProperty("class", "status-indicator")
        gpu_status.setStyleSheet("color: #4CAF50;")
        gpu_layout.addWidget(gpu_label)
        gpu_layout.addWidget(gpu_status)
        gpu_layout.addStretch()
        self.add_layout(gpu_layout)

        # 内存状态
        memory_layout = QHBoxLayout()
        memory_label = QLabel("内存:")
        memory_label.setProperty("class", "status-label")
        memory_status = QLabel("●")
        memory_status.setProperty("class", "status-indicator")
        memory_status.setStyleSheet("color: #FF9800;")
        memory_layout.addWidget(memory_label)
        memory_layout.addWidget(memory_status)
        memory_layout.addStretch()
        self.add_layout(memory_layout)

        # 网络状态
        network_layout = QHBoxLayout()
        network_label = QLabel("网络:")
        network_label.setProperty("class", "status-label")
        network_status = QLabel("●")
        network_status.setProperty("class", "status-indicator")
        network_status.setStyleSheet("color: #4CAF50;")
        network_layout.addWidget(network_label)
        network_layout.addWidget(network_status)
        network_layout.addStretch()
        self.add_layout(network_layout)


class ProgressCard(ModernCard):
    """进度显示卡片"""
    
    def __init__(self, parent=None):
        super().__init__("量化进度", parent)
        self.setup_progress_ui()
    
    def setup_progress_ui(self):
        """设置进度UI"""
        progress_items = [
            ("分镜脚本", 85),
            ("图像生成", 60),
            ("音频合成", 30),
            ("视频制作", 10)
        ]
        
        for name, value in progress_items:
            # 进度项布局
            item_layout = QVBoxLayout()
            
            # 标签和百分比
            label_layout = QHBoxLayout()
            name_label = QLabel(name)
            name_label.setProperty("class", "progress-name")
            name_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;")

            value_label = QLabel(f"{value}%")
            value_label.setProperty("class", "progress-value")
            value_label.setStyleSheet("""
                font-size: 16px;
                font-weight: bold;
                color: #2196F3;
                background-color: #E3F2FD;
                padding: 4px 8px;
                border-radius: 4px;
                min-width: 50px;
                text-align: center;
            """)
            value_label.setAlignment(Qt.AlignCenter)

            label_layout.addWidget(name_label)
            label_layout.addStretch()
            label_layout.addWidget(value_label)

            # 进度条
            progress_bar = QProgressBar()
            progress_bar.setValue(value)
            progress_bar.setMaximumHeight(8)
            progress_bar.setProperty("class", "modern-progress")
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #E0E0E0;
                    border-radius: 4px;
                    background-color: #F5F5F5;
                    text-align: center;
                    font-size: 12px;
                    font-weight: bold;
                    color: #333333;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                              stop: 0 #42a5f5, stop: 1 #1976d2);
                    border-radius: 3px;
                }
            """)
            
            item_layout.addLayout(label_layout)
            item_layout.addWidget(progress_bar)
            item_layout.addSpacing(8)
            
            self.add_layout(item_layout)


class ModernCardMainWindow(QMainWindow):
    """现代化卡片式主窗口"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化控制器
        self.app_controller = AppController()
        self.project_manager = ProjectManager()
        
        # 当前选中的页面
        self.current_page = None
        
        # 设置窗口
        self.setup_window()
        self.setup_ui()
        self.setup_connections()
        
        # 默认选中第一个页面
        self.switch_to_page("workflow")
        
        logger.info("现代化卡片式主窗口初始化完成")
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("🎬 AI视频生成器 - 现代化界面")
        self.setMinimumSize(1400, 900)
        
        # 居中显示
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
        
        # 应用现代化样式
        apply_modern_card_styles(self)

    def setup_ui(self):
        """设置用户界面"""
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 水平分割器
        main_splitter = QSplitter(Qt.Horizontal)
        central_layout = QHBoxLayout(central_widget)
        central_layout.setContentsMargins(12, 12, 12, 12)
        central_layout.addWidget(main_splitter)

        # 左侧导航栏
        self.setup_sidebar(main_splitter)

        # 中央内容区域
        self.setup_content_area(main_splitter)

        # 右侧信息面板
        self.setup_info_panel(main_splitter)

        # 设置分割器比例 (导航:内容:信息 = 1:3:1)
        main_splitter.setSizes([200, 800, 300])
        main_splitter.setCollapsible(0, False)  # 导航栏不可折叠
        main_splitter.setCollapsible(2, True)   # 信息面板可折叠

    def setup_sidebar(self, parent_splitter):
        """设置左侧导航栏"""
        sidebar_widget = QWidget()
        sidebar_widget.setMaximumWidth(220)
        sidebar_widget.setMinimumWidth(180)

        sidebar_layout = QVBoxLayout(sidebar_widget)
        sidebar_layout.setContentsMargins(8, 16, 8, 16)
        sidebar_layout.setSpacing(12)

        # 导航标题
        nav_title = QLabel("🎬 导航菜单")
        nav_title.setFont(QFont("Microsoft YaHei UI", 12, QFont.Bold))
        nav_title.setProperty("class", "nav-title")
        nav_title.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(nav_title)

        # 导航按钮
        self.nav_buttons = {}
        nav_items = [
            ("workflow", "🎭 工作流程"),
            ("text_creation", "📝 文章创作"),
            ("storyboard", "🎬 分镜脚本"),
            ("image", "🖼️ 图像生成"),
            ("voice", "🎵 配音制作"),
            ("video", "🎞️ 视频合成"),
            ("consistency", "🎨 一致性控制"),
            ("project", "📁 项目管理"),
            ("settings", "⚙️ 系统设置")
        ]

        for page_id, text in nav_items:
            btn = ModernCardButton(text)
            btn.clicked.connect(lambda checked, pid=page_id: self.switch_to_page(pid))
            self.nav_buttons[page_id] = btn
            sidebar_layout.addWidget(btn)

        sidebar_layout.addStretch()

        # 切换主题按钮
        theme_btn = ModernCardButton("🌙 切换主题")
        theme_btn.clicked.connect(self.toggle_theme)
        sidebar_layout.addWidget(theme_btn)

        parent_splitter.addWidget(sidebar_widget)

    def setup_content_area(self, parent_splitter):
        """设置中央内容区域"""
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(8, 8, 8, 8)
        content_layout.setSpacing(12)

        # 顶部工具栏卡片
        self.setup_toolbar_card(content_layout)

        # 主要内容区域 - 使用堆叠控件
        self.content_stack = QStackedWidget()
        content_layout.addWidget(self.content_stack)

        # 创建各个页面
        self.create_pages()

        parent_splitter.addWidget(content_widget)

    def setup_toolbar_card(self, parent_layout):
        """设置顶部工具栏卡片"""
        toolbar_card = ModernCard("🎯 项目工作台")
        toolbar_layout = QHBoxLayout()

        # 项目操作按钮
        project_buttons = [
            ("新建项目", "toolbar-button-green", self.new_project),
            ("打开项目", "toolbar-button-blue", self.open_project),
            ("保存项目", "toolbar-button-orange", self.save_project),
            ("刷新", "toolbar-button-purple", self.refresh_project)
        ]

        for text, style_class, handler in project_buttons:
            btn = QPushButton(text)
            btn.setMinimumHeight(36)
            btn.setFont(QFont("Microsoft YaHei UI", 9, QFont.Medium))
            btn.setProperty("class", f"toolbar-button {style_class}")
            btn.clicked.connect(handler)
            toolbar_layout.addWidget(btn)

        toolbar_layout.addStretch()

        # 版本信息
        version_label = QLabel("v2.0.0")
        version_label.setFont(QFont("Microsoft YaHei UI", 8))
        version_label.setProperty("class", "version-label")
        toolbar_layout.addWidget(version_label)

        toolbar_card.add_layout(toolbar_layout)
        parent_layout.addWidget(toolbar_card)

    def setup_info_panel(self, parent_splitter):
        """设置右侧信息面板"""
        info_widget = QWidget()
        info_widget.setMaximumWidth(320)
        info_widget.setMinimumWidth(280)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(8, 16, 8, 16)
        info_layout.setSpacing(16)

        # 信息面板标题
        info_title = QLabel("📊 信息面板")
        info_title.setFont(QFont("Microsoft YaHei UI", 12, QFont.Bold))
        info_title.setProperty("class", "info-title")
        info_title.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(info_title)

        # 帮助信息卡片
        help_card = ModernCard("💡 帮助")
        help_items = [
            "🎯 建议",
            "📚 五步骤实战",
            "🎨 配色合成",
            "🔧 系统更新可用"
        ]

        for item in help_items:
            help_label = QLabel(item)
            help_label.setFont(QFont("Microsoft YaHei UI", 9))
            help_label.setProperty("class", "help-item")
            help_card.add_widget(help_label)

        info_layout.addWidget(help_card)

        # 系统状态卡片
        self.status_card = StatusCard()
        info_layout.addWidget(self.status_card)

        # 进度卡片
        self.progress_card = ProgressCard()
        info_layout.addWidget(self.progress_card)

        info_layout.addStretch()

        parent_splitter.addWidget(info_widget)

    def create_pages(self):
        """创建各个功能页面"""
        self.pages = {}

        # 工作流程页面
        self.pages["workflow"] = self.create_workflow_page()
        self.content_stack.addWidget(self.pages["workflow"])

        # 文本创作页面
        self.pages["text_creation"] = self.create_text_creation_page()
        self.content_stack.addWidget(self.pages["text_creation"])

        # 项目管理页面
        self.pages["project"] = self.create_project_page()
        self.content_stack.addWidget(self.pages["project"])

        # 分镜脚本页面
        self.pages["storyboard"] = FiveStageStoryboardTab(self)
        self.content_stack.addWidget(self.pages["storyboard"])

        # 图像生成页面
        self.pages["image"] = StoryboardImageGenerationTab(self.app_controller, self.project_manager, self)
        self.content_stack.addWidget(self.pages["image"])

        # 配音制作页面
        self.pages["voice"] = VoiceGenerationTab(self.app_controller, self.project_manager, self)
        self.content_stack.addWidget(self.pages["voice"])

        # 视频合成页面
        self.pages["video"] = VideoGenerationTab(self.app_controller, self.project_manager, self)
        self.content_stack.addWidget(self.pages["video"])

        # 一致性控制页面
        self.pages["consistency"] = ConsistencyControlPanel(None, self.project_manager, self)
        self.content_stack.addWidget(self.pages["consistency"])

        # 系统设置页面
        self.pages["settings"] = SettingsTab(self)
        self.content_stack.addWidget(self.pages["settings"])

    def create_workflow_page(self):
        """创建工作流程页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)

        # 当前项目卡片
        current_project_card = ModernCard("📋 当前项目")

        # 项目信息
        project_info_layout = QVBoxLayout()
        project_name_label = QLabel("我的AI视频项目")
        project_name_label.setFont(QFont("Microsoft YaHei UI", 11, QFont.Bold))
        project_name_label.setStyleSheet("color: #333333;")

        project_desc_label = QLabel("这是一个使用AI技术生成的视频项目，包含智能分镜、自动配音等功能。")
        project_desc_label.setFont(QFont("Microsoft YaHei UI", 9))
        project_desc_label.setStyleSheet("color: #666666; line-height: 1.4;")
        project_desc_label.setWordWrap(True)

        project_info_layout.addWidget(project_name_label)
        project_info_layout.addWidget(project_desc_label)
        current_project_card.add_layout(project_info_layout)

        layout.addWidget(current_project_card)

        # 快捷操作卡片
        quick_actions_card = ModernCard("⚡ 快捷操作")
        actions_grid = QGridLayout()

        quick_actions = [
            ("🎬 生成分镜", "storyboard"),
            ("🖼️ 创建图像", "image"),
            ("🎵 合成配音", "voice"),
            ("🎞️ 导出视频", "video")
        ]

        for i, (text, page_id) in enumerate(quick_actions):
            btn = QPushButton(text)
            btn.setMinimumHeight(60)
            btn.setFont(QFont("Microsoft YaHei UI", 10, QFont.Medium))
            btn.setProperty("class", "quick-action-button")
            btn.clicked.connect(lambda checked, pid=page_id: self.switch_to_page(pid))
            actions_grid.addWidget(btn, i // 2, i % 2)

        quick_actions_card.add_layout(actions_grid)
        layout.addWidget(quick_actions_card)

        layout.addStretch()
        return page

    def create_text_creation_page(self):
        """创建美观的文章创作页面"""
        from PyQt5.QtWidgets import QGroupBox, QTextEdit, QComboBox, QHBoxLayout, QFormLayout, QSizePolicy
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont, QIcon

        page = QWidget()
        page.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
            }
        """)

        # 主滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: #f1f3f4;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #c1c8cd;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #a8b1ba;
            }
        """)

        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # 页面标题
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)

        title_label = QLabel("✨ 智能文章创作工坊")
        title_label.setFont(QFont("Microsoft YaHei UI", 24, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: transparent;
                padding: 10px 0;
            }
        """)

        subtitle_label = QLabel("让AI助力您的创作灵感")
        subtitle_label.setFont(QFont("Microsoft YaHei UI", 12))
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                background: transparent;
                margin-left: 10px;
            }
        """)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        title_layout.addStretch()
        layout.addWidget(title_widget)

        # 创作配置卡片
        config_card = self.create_beautiful_card("🎨 创作配置", "#e74c3c")
        config_layout = QVBoxLayout()
        config_layout.setContentsMargins(25, 20, 25, 20)
        config_layout.setSpacing(15)

        # 风格选择
        style_row = QWidget()
        style_row_layout = QHBoxLayout(style_row)
        style_row_layout.setContentsMargins(0, 0, 0, 0)

        style_label = QLabel("创作风格")
        style_label.setFont(QFont("Microsoft YaHei UI", 11, QFont.Medium))
        style_label.setStyleSheet("color: #2c3e50; min-width: 80px;")

        self.style_combo = QComboBox()
        self.style_combo.addItems([
            "🎬 电影风格", "🎌 动漫风格", "🌸 吉卜力风格", "🌃 赛博朋克风格",
            "🎨 水彩插画风格", "🎮 像素风格", "📸 写实摄影风格"
        ])
        self.style_combo.setMinimumHeight(40)
        self.style_combo.setStyleSheet("""
            QComboBox {
                padding: 8px 15px;
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                background: white;
                font-size: 13px;
                color: #2c3e50;
                selection-background-color: #3498db;
            }
            QComboBox:hover {
                border-color: #3498db;
                background: #f8f9fa;
            }
            QComboBox:focus {
                border-color: #2980b9;
                background: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
                background: transparent;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 6px solid #7f8c8d;
                margin-right: 8px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                background: white;
                selection-background-color: #e8f4fd;
                outline: none;
            }
        """)

        style_row_layout.addWidget(style_label)
        style_row_layout.addWidget(self.style_combo, 1)
        config_layout.addWidget(style_row)

        config_card.add_layout(config_layout)
        layout.addWidget(config_card)

        # 文本输入卡片
        input_card = self.create_beautiful_card("📝 文本输入", "#3498db")
        input_layout = QVBoxLayout()
        input_layout.setContentsMargins(25, 20, 25, 25)
        input_layout.setSpacing(15)

        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText("✍️ 在这里输入您的创作内容，或者描述您想要创作的故事主题...\n\n💡 提示：您可以输入故事大纲、角色设定、情节描述等，AI将帮助您完善和扩展内容。")
        self.text_input.setMinimumHeight(200)
        self.text_input.setFont(QFont("Microsoft YaHei UI", 12))
        self.text_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ecf0f1;
                border-radius: 12px;
                padding: 15px;
                background: white;
                color: #2c3e50;
                line-height: 1.6;
                selection-background-color: #e8f4fd;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background: #fdfdfd;
            }
            QTextEdit:hover {
                border-color: #bdc3c7;
            }
        """)
        input_layout.addWidget(self.text_input)

        input_card.add_layout(input_layout)
        layout.addWidget(input_card)

        # 操作按钮卡片
        actions_card = self.create_beautiful_card("🚀 AI创作助手", "#9b59b6")
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(25, 20, 25, 20)
        actions_layout.setSpacing(15)

        # AI创作按钮
        ai_create_btn = QPushButton("🎭 AI创作故事")
        ai_create_btn.setMinimumHeight(50)
        ai_create_btn.setFont(QFont("Microsoft YaHei UI", 12, QFont.Medium))
        ai_create_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 20px;
                font-weight: 600;
                text-align: center;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #5dade2, stop: 1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #2980b9, stop: 1 #1f618d);
            }
        """)
        ai_create_btn.clicked.connect(self.ai_create_story)
        actions_layout.addWidget(ai_create_btn)

        # 文本改写按钮
        rewrite_btn = QPushButton("✨ AI改写优化")
        rewrite_btn.setMinimumHeight(50)
        rewrite_btn.setFont(QFont("Microsoft YaHei UI", 12, QFont.Medium))
        rewrite_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #2ecc71, stop: 1 #27ae60);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 20px;
                font-weight: 600;
                text-align: center;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #58d68d, stop: 1 #2ecc71);
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #27ae60, stop: 1 #1e8449);
            }
        """)
        rewrite_btn.clicked.connect(self.rewrite_text)
        actions_layout.addWidget(rewrite_btn)

        actions_card.add_layout(actions_layout)
        layout.addWidget(actions_card)

        # 结果展示卡片
        result_card = self.create_beautiful_card("🎯 创作结果", "#e67e22")
        result_layout = QVBoxLayout()
        result_layout.setContentsMargins(25, 20, 25, 25)
        result_layout.setSpacing(15)

        self.rewritten_text = QTextEdit()
        self.rewritten_text.setPlaceholderText("🎉 AI创作的精彩内容将在这里呈现...\n\n📖 您可以在这里查看AI生成的故事、改写的文本或优化建议。")
        self.rewritten_text.setMinimumHeight(250)
        self.rewritten_text.setFont(QFont("Microsoft YaHei UI", 12))
        self.rewritten_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ecf0f1;
                border-radius: 12px;
                padding: 15px;
                background: #f8f9fa;
                color: #2c3e50;
                line-height: 1.6;
                selection-background-color: #fff3cd;
            }
            QTextEdit:focus {
                border-color: #f39c12;
                background: white;
            }
            QTextEdit:hover {
                border-color: #bdc3c7;
            }
        """)
        result_layout.addWidget(self.rewritten_text)

        result_card.add_layout(result_layout)
        layout.addWidget(result_card)

        layout.addStretch()

        scroll_area.setWidget(content_widget)

        page_layout = QVBoxLayout(page)
        page_layout.setContentsMargins(0, 0, 0, 0)
        page_layout.addWidget(scroll_area)

        return page

    def create_beautiful_card(self, title, accent_color="#3498db"):
        """创建美观的卡片组件"""
        card = ModernCard(title)
        card.setStyleSheet(f"""
            QFrame {{
                background: white;
                border: 2px solid #ecf0f1;
                border-radius: 16px;
                border-left: 4px solid {accent_color};
                margin: 4px;
                padding: 2px;
            }}
            QFrame:hover {{
                border-color: #bdc3c7;
                background: #fdfdfd;
            }}
            QLabel {{
                color: #2c3e50;
                font-weight: 600;
                font-size: 14px;
                background: transparent;
                padding: 8px 0;
            }}
        """)
        return card

    def create_project_page(self):
        """创建项目管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)

        # 项目文件卡片
        project_files_card = ModernCard("📁 项目文件")
        files_layout = QVBoxLayout()

        # 文件列表
        file_items = [
            ("🎵 音频文件.mp4", "file-button-green"),
            ("🖼️ 海报设计.png", "file-button-blue"),
            ("📝 脚本草稿.txt", "file-button-orange"),
            ("🎵 背景音乐.mp3", "file-button-purple")
        ]

        for filename, style_class in file_items:
            file_btn = QPushButton(filename)
            file_btn.setMinimumHeight(40)
            file_btn.setFont(QFont("Microsoft YaHei UI", 9))
            file_btn.setProperty("class", f"file-button {style_class}")
            files_layout.addWidget(file_btn)

        project_files_card.add_layout(files_layout)
        layout.addWidget(project_files_card)

        layout.addStretch()
        return page

    def setup_connections(self):
        """设置信号连接"""
        # 这里可以添加各种信号连接
        pass

    def switch_to_page(self, page_id):
        """切换到指定页面"""
        if page_id in self.pages:
            # 更新按钮状态
            for btn_id, btn in self.nav_buttons.items():
                btn.setChecked(btn_id == page_id)

            # 切换页面
            self.content_stack.setCurrentWidget(self.pages[page_id])
            self.current_page = page_id

            logger.info(f"切换到页面: {page_id}")

    def toggle_theme(self):
        """切换主题"""
        # 这里可以实现主题切换逻辑
        logger.info("主题切换功能待实现")

    def new_project(self):
        """新建项目"""
        try:
            from src.gui.project_dialog import NewProjectDialog
            from src.utils.ui_utils import show_success

            dialog = NewProjectDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                project_info = dialog.get_project_info()

                # 创建新项目
                if self.project_manager.create_new_project(
                    project_info["name"],
                    project_info["description"]
                ):
                    show_success(f"项目 '{project_info['name']}' 创建成功！")
                    self.setWindowTitle(f"AI 视频生成系统 - {project_info['name']}")
                    logger.info(f"新项目创建成功: {project_info['name']}")
                else:
                    QMessageBox.critical(self, "错误", "项目创建失败！")

        except Exception as e:
            logger.error(f"新建项目失败: {e}")
            QMessageBox.critical(self, "错误", f"新建项目失败：{e}")

    def open_project(self):
        """打开项目"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from src.utils.ui_utils import show_success

            # 选择项目文件
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择项目文件",
                "",
                "项目文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                if hasattr(self, 'project_manager') and self.project_manager:
                    if self.project_manager.load_project(file_path):
                        project_name = self.project_manager.current_project.get("project_name", "未知项目")
                        show_success(f"项目 '{project_name}' 打开成功！")
                        self.setWindowTitle(f"AI 视频生成系统 - {project_name}")
                        logger.info(f"项目打开成功: {project_name}")
                    else:
                        QMessageBox.critical(self, "错误", "项目打开失败！")
                else:
                    QMessageBox.warning(self, "警告", "项目管理器未初始化！")

        except Exception as e:
            logger.error(f"打开项目失败: {e}")
            QMessageBox.critical(self, "错误", f"打开项目失败：{e}")

    def save_project(self):
        """保存项目"""
        try:
            from src.utils.ui_utils import show_success

            if not hasattr(self, 'project_manager') or not self.project_manager:
                QMessageBox.warning(self, "警告", "项目管理器未初始化！")
                return

            if not self.project_manager.current_project:
                QMessageBox.warning(self, "警告", "没有打开的项目可以保存！")
                return

            if self.project_manager.save_project():
                show_success("项目保存成功！")
                logger.info("项目保存成功")
            else:
                QMessageBox.critical(self, "错误", "项目保存失败！")

        except Exception as e:
            logger.error(f"保存项目失败: {e}")
            QMessageBox.critical(self, "错误", f"保存项目失败：{e}")

    def refresh_project(self):
        """刷新项目"""
        try:
            from src.utils.ui_utils import show_success

            if not hasattr(self, 'project_manager') or not self.project_manager:
                QMessageBox.warning(self, "警告", "项目管理器未初始化！")
                return

            if not self.project_manager.current_project:
                QMessageBox.information(self, "提示", "当前没有打开的项目")
                return

            # 重新加载项目数据
            project_path = self.project_manager.current_project.get("project_dir")
            if project_path:
                project_file = os.path.join(project_path, "project.json")
                if os.path.exists(project_file):
                    self.project_manager.load_project(project_file)
                    show_success("项目数据刷新完成！")
                    logger.info("项目数据刷新完成")
                else:
                    QMessageBox.warning(self, "警告", "项目文件不存在！")
            else:
                QMessageBox.warning(self, "警告", "无法确定项目路径！")

        except Exception as e:
            logger.error(f"刷新项目失败: {e}")
            QMessageBox.critical(self, "错误", f"刷新项目失败：{e}")

    def ai_create_story(self):
        """AI创作故事"""
        try:
            from src.utils.ui_utils import show_success
            from PyQt5.QtWidgets import QInputDialog

            # 获取创作主题
            theme, ok = QInputDialog.getText(
                self,
                "AI故事创作",
                "请输入故事主题或关键词："
            )

            if not ok or not theme.strip():
                return

            # 这里应该调用AI创作功能
            # 暂时使用模拟内容
            created_story = f"基于主题'{theme}'创作的故事：\n\n这是一个关于{theme}的精彩故事..."

            self.rewritten_text.setPlainText(created_story)
            show_success("AI故事创作完成！")
            logger.info(f"AI故事创作完成，主题: {theme}")

        except Exception as e:
            logger.error(f"AI创作故事失败: {e}")
            QMessageBox.critical(self, "错误", f"AI创作故事失败：{e}")

    def rewrite_text(self):
        """AI改写文本"""
        try:
            from src.utils.ui_utils import show_success

            text = self.text_input.toPlainText().strip()
            if not text:
                QMessageBox.warning(self, "警告", "请先输入要改写的文本内容")
                return

            # 这里应该调用AI改写功能
            # 暂时使用模拟内容
            rewritten = f"改写后的文本：\n\n{text}\n\n（已进行AI优化改写）"

            self.rewritten_text.setPlainText(rewritten)
            show_success("文本改写完成！")
            logger.info("文本改写完成")

        except Exception as e:
            logger.error(f"文本改写失败: {e}")
            QMessageBox.critical(self, "错误", f"文本改写失败：{e}")




# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle("Fusion")

    window = ModernCardMainWindow()
    window.show()

    sys.exit(app.exec_())
