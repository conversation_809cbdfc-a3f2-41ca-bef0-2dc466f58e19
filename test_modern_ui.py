#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化UI测试程序
测试Material Design风格的界面组件和效果
"""

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox, QListWidget,
    QTabWidget, QGroupBox, QProgressBar, QSlider, QCheckBox, QRadioButton,
    QSpacerItem, QSizePolicy, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.gui.styles import apply_modern_style, toggle_theme
except ImportError:
    def apply_modern_style():
        pass
    def toggle_theme():
        pass
from src.gui.modern_ui_components import (
    MaterialButton, MaterialCard, MaterialLineEdit, MaterialTextEdit,
    MaterialComboBox, MaterialListWidget, MaterialTabWidget,
    FloatingActionButton, StatusIndicator, LoadingSpinner
)
from src.gui.animation_effects import animate_widget_entrance, get_animation_manager
from src.gui.responsive_layout import ResponsiveWidget


class ModernUITestWindow(ResponsiveWidget):
    """现代化UI测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_demo_content()
        self.apply_modern_style()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("AI视频生成器 - 现代化UI测试")
        self.setMinimumSize(1000, 700)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 创建主内容控件
        content_widget = QWidget()
        scroll_area.setWidget(content_widget)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll_area)
        
        # 内容布局
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(24)
        layout.setContentsMargins(24, 24, 24, 24)
        
        # 标题
        title = QLabel("现代化UI组件展示")
        title.setFont(QFont("Segoe UI", 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 创建各个演示区域
        self.create_button_demo(layout)
        self.create_input_demo(layout)
        self.create_list_demo(layout)
        self.create_tab_demo(layout)
        self.create_status_demo(layout)
        self.create_animation_demo(layout)
        
        # 底部间距
        layout.addStretch()
    
    def create_button_demo(self, parent_layout):
        """创建按钮演示"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        
        title = QLabel("按钮组件")
        title.setFont(QFont("Segoe UI", 16, QFont.Medium))
        layout.addWidget(title)
        
        # 按钮行
        button_layout = QHBoxLayout()
        
        # 填充按钮
        filled_btn = MaterialButton("填充按钮", "filled")
        filled_btn.clicked.connect(lambda: self.show_message("填充按钮被点击"))
        button_layout.addWidget(filled_btn)
        
        # 轮廓按钮
        outlined_btn = MaterialButton("轮廓按钮", "outlined")
        outlined_btn.clicked.connect(lambda: self.show_message("轮廓按钮被点击"))
        button_layout.addWidget(outlined_btn)
        
        # 文本按钮
        text_btn = MaterialButton("文本按钮", "text")
        text_btn.clicked.connect(lambda: self.show_message("文本按钮被点击"))
        button_layout.addWidget(text_btn)
        
        # 主题切换按钮
        theme_btn = MaterialButton("🌙 切换主题", "text")
        theme_btn.clicked.connect(self.toggle_theme)
        button_layout.addWidget(theme_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 悬浮操作按钮
        fab_layout = QHBoxLayout()
        fab = FloatingActionButton("➕")
        fab.clicked.connect(lambda: self.show_message("悬浮按钮被点击"))
        fab_layout.addWidget(fab)
        fab_layout.addStretch()
        layout.addLayout(fab_layout)
        
        parent_layout.addWidget(card)
    
    def create_input_demo(self, parent_layout):
        """创建输入组件演示"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        
        title = QLabel("输入组件")
        title.setFont(QFont("Segoe UI", 16, QFont.Medium))
        layout.addWidget(title)
        
        # 输入框
        line_edit = MaterialLineEdit()
        line_edit.setPlaceholderText("请输入文本...")
        layout.addWidget(line_edit)
        
        # 文本编辑器
        text_edit = MaterialTextEdit()
        text_edit.setPlaceholderText("请输入多行文本...")
        text_edit.setMaximumHeight(100)
        layout.addWidget(text_edit)
        
        # 下拉框
        combo_box = MaterialComboBox()
        combo_box.addItems(["选项1", "选项2", "选项3", "选项4"])
        layout.addWidget(combo_box)
        
        # 进度条和滑块
        controls_layout = QHBoxLayout()
        
        progress_bar = QProgressBar()
        progress_bar.setValue(65)
        controls_layout.addWidget(progress_bar)
        
        slider = QSlider(Qt.Horizontal)
        slider.setValue(65)
        slider.valueChanged.connect(progress_bar.setValue)
        controls_layout.addWidget(slider)
        
        layout.addLayout(controls_layout)
        
        # 复选框和单选框
        check_layout = QHBoxLayout()
        
        checkbox = QCheckBox("复选框选项")
        checkbox.setChecked(True)
        check_layout.addWidget(checkbox)
        
        radio1 = QRadioButton("单选框1")
        radio1.setChecked(True)
        check_layout.addWidget(radio1)
        
        radio2 = QRadioButton("单选框2")
        check_layout.addWidget(radio2)
        
        check_layout.addStretch()
        layout.addLayout(check_layout)
        
        parent_layout.addWidget(card)
    
    def create_list_demo(self, parent_layout):
        """创建列表演示"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        
        title = QLabel("列表组件")
        title.setFont(QFont("Segoe UI", 16, QFont.Medium))
        layout.addWidget(title)
        
        list_widget = MaterialListWidget()
        list_widget.setMaximumHeight(150)
        
        items = [
            "📝 文本创作",
            "🎬 分镜设计", 
            "🎨 图像生成",
            "🎤 配音制作",
            "🎥 视频合成",
            "📤 导出发布"
        ]
        
        for item in items:
            list_widget.addItem(item)
        
        layout.addWidget(list_widget)
        parent_layout.addWidget(card)
    
    def create_tab_demo(self, parent_layout):
        """创建标签页演示"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        
        title = QLabel("标签页组件")
        title.setFont(QFont("Segoe UI", 16, QFont.Medium))
        layout.addWidget(title)
        
        tab_widget = MaterialTabWidget()
        
        # 标签页1
        tab1 = QWidget()
        tab1_layout = QVBoxLayout(tab1)
        tab1_layout.addWidget(QLabel("这是第一个标签页的内容"))
        tab_widget.addTab(tab1, "标签页1")
        
        # 标签页2
        tab2 = QWidget()
        tab2_layout = QVBoxLayout(tab2)
        tab2_layout.addWidget(QLabel("这是第二个标签页的内容"))
        tab_widget.addTab(tab2, "标签页2")
        
        # 标签页3
        tab3 = QWidget()
        tab3_layout = QVBoxLayout(tab3)
        tab3_layout.addWidget(QLabel("这是第三个标签页的内容"))
        tab_widget.addTab(tab3, "标签页3")
        
        tab_widget.setMaximumHeight(200)
        layout.addWidget(tab_widget)
        parent_layout.addWidget(card)
    
    def create_status_demo(self, parent_layout):
        """创建状态指示器演示"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        
        title = QLabel("状态指示器")
        title.setFont(QFont("Segoe UI", 16, QFont.Medium))
        layout.addWidget(title)
        
        status_layout = QHBoxLayout()
        
        # 各种状态指示器
        statuses = [
            ("online", "在线"),
            ("offline", "离线"),
            ("warning", "警告"),
            ("unknown", "未知")
        ]
        
        for status, text in statuses:
            indicator_layout = QHBoxLayout()
            indicator = StatusIndicator(status)
            label = QLabel(text)
            indicator_layout.addWidget(indicator)
            indicator_layout.addWidget(label)
            indicator_layout.addStretch()
            
            status_widget = QWidget()
            status_widget.setLayout(indicator_layout)
            status_layout.addWidget(status_widget)
        
        status_layout.addStretch()
        layout.addLayout(status_layout)
        
        # 加载动画
        loading_layout = QHBoxLayout()
        spinner = LoadingSpinner(32)
        spinner.start_animation()
        loading_layout.addWidget(spinner)
        loading_layout.addWidget(QLabel("加载中..."))
        loading_layout.addStretch()
        layout.addLayout(loading_layout)
        
        parent_layout.addWidget(card)
    
    def create_animation_demo(self, parent_layout):
        """创建动画演示"""
        card = MaterialCard()
        layout = QVBoxLayout(card)
        
        title = QLabel("动画效果")
        title.setFont(QFont("Segoe UI", 16, QFont.Medium))
        layout.addWidget(title)
        
        # 动画按钮
        animation_layout = QHBoxLayout()
        
        entrance_btn = MaterialButton("入场动画", "outlined")
        entrance_btn.clicked.connect(lambda: animate_widget_entrance(card))
        animation_layout.addWidget(entrance_btn)
        
        bounce_btn = MaterialButton("弹跳动画", "outlined")
        bounce_btn.clicked.connect(self.bounce_card)
        animation_layout.addWidget(bounce_btn)
        
        glow_btn = MaterialButton("发光效果", "outlined")
        glow_btn.clicked.connect(self.toggle_glow)
        animation_layout.addWidget(glow_btn)
        
        animation_layout.addStretch()
        layout.addLayout(animation_layout)
        
        self.animation_card = card
        parent_layout.addWidget(card)
    
    def setup_demo_content(self):
        """设置演示内容"""
        # 延迟显示入场动画
        QTimer.singleShot(500, self.show_entrance_animations)
    
    def show_entrance_animations(self):
        """显示入场动画"""
        cards = self.findChildren(MaterialCard)
        for i, card in enumerate(cards):
            QTimer.singleShot(i * 100, lambda c=card: animate_widget_entrance(c))
    
    def apply_modern_style(self):
        """应用现代化样式"""
        apply_modern_style()
    
    def toggle_theme(self):
        """切换主题"""
        toggle_theme()
    
    def show_message(self, message):
        """显示消息"""
        print(f"消息: {message}")
        if hasattr(self, 'statusBar'):
            self.statusBar().showMessage(message, 2000)
    
    def bounce_card(self):
        """弹跳卡片"""
        if hasattr(self, 'animation_card'):
            manager = get_animation_manager()
            bounce_animation = manager.create_bounce_animation(self.animation_card)
            manager.play_animation("bounce_demo", bounce_animation)
    
    def toggle_glow(self):
        """切换发光效果"""
        if hasattr(self, 'animation_card'):
            manager = get_animation_manager()
            if "glow_demo" in manager.animations:
                manager.animations["glow_demo"].stop()
                self.animation_card.setGraphicsEffect(None)
                del manager.animations["glow_demo"]
            else:
                glow_animation = manager.create_glow_animation(self.animation_card)
                manager.play_animation("glow_demo", glow_animation)
    
    def adjust_layout_for_breakpoint(self, breakpoint: str):
        """根据断点调整布局"""
        super().adjust_layout_for_breakpoint(breakpoint)
        print(f"布局已调整为断点: {breakpoint}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("AI视频生成器")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("AI Video Generator")
    
    # 创建测试窗口
    window = ModernUITestWindow()
    window.show()
    
    print("现代化UI测试程序已启动")
    print("功能说明:")
    print("- 测试Material Design风格的界面组件")
    print("- 演示响应式布局和DPI适配")
    print("- 展示动画和过渡效果")
    print("- 支持深色/浅色主题切换")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
