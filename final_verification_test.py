#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证测试
检查所有旧样式修复和界面优化
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QFont

from src.gui.styles import UnifiedThemeSystem, ThemeMode, get_theme_system
from src.gui.modern_ui_components import MaterialButton, MaterialCard
from src.utils.logger import logger


class FinalVerificationWindow(QMainWindow):
    """最终验证窗口"""
    
    def __init__(self):
        super().__init__()
        self.theme_system = get_theme_system()
        self.init_ui()
        self.apply_theme()
        self.run_verification_tests()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("✅ 最终验证测试 - AI视频生成器")
        self.setGeometry(200, 200, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 标题
        title = QLabel("🔍 系统验证测试")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        layout.addWidget(title)
        
        # 测试结果卡片
        self.results_card = MaterialCard()
        results_layout = QVBoxLayout(self.results_card)
        results_layout.setContentsMargins(20, 20, 20, 20)
        
        self.results_label = QLabel("正在运行验证测试...")
        self.results_label.setWordWrap(True)
        results_layout.addWidget(self.results_label)
        
        layout.addWidget(self.results_card)
        
        # 主题切换按钮
        theme_btn = MaterialButton("🌙 切换主题测试")
        theme_btn.clicked.connect(self.test_theme_switching)
        layout.addWidget(theme_btn)
        
        # 关闭按钮
        close_btn = MaterialButton("✅ 测试完成")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
    
    def apply_theme(self):
        """应用主题"""
        try:
            self.theme_system.apply_to_widget(self)
            logger.info("主题应用成功")
        except Exception as e:
            logger.error(f"主题应用失败: {e}")
    
    def run_verification_tests(self):
        """运行验证测试"""
        results = []
        
        # 测试1: 主题系统
        try:
            current_mode = self.theme_system.get_current_mode()
            results.append(f"✅ 主题系统正常 - 当前模式: {current_mode.value}")
        except Exception as e:
            results.append(f"❌ 主题系统错误: {e}")
        
        # 测试2: 样式应用
        try:
            self.theme_system.apply_to_widget(self)
            results.append("✅ 样式应用正常")
        except Exception as e:
            results.append(f"❌ 样式应用错误: {e}")
        
        # 测试3: 颜色系统
        try:
            colors = self.theme_system.colors
            if colors and colors.get('primary'):
                results.append("✅ 颜色系统正常")
            else:
                results.append("❌ 颜色系统缺少必要颜色")
        except Exception as e:
            results.append(f"❌ 颜色系统错误: {e}")
        
        # 测试4: 组件创建
        try:
            test_button = MaterialButton("测试按钮")
            test_card = MaterialCard()
            results.append("✅ UI组件创建正常")
        except Exception as e:
            results.append(f"❌ UI组件创建错误: {e}")
        
        # 测试5: 检查旧样式清理
        old_style_issues = self.check_old_style_cleanup()
        if not old_style_issues:
            results.append("✅ 旧样式清理完成")
        else:
            results.append(f"⚠️ 发现旧样式残留: {len(old_style_issues)}个问题")
        
        # 更新结果显示
        results_text = "\n".join(results)
        self.results_label.setText(results_text)
        
        logger.info("验证测试完成")
        logger.info(f"测试结果:\n{results_text}")
    
    def check_old_style_cleanup(self):
        """检查旧样式清理情况"""
        issues = []
        
        # 这里可以添加更多检查逻辑
        # 目前主要检查是否能正常导入新的样式系统
        try:
            from src.gui.styles import UnifiedThemeSystem, get_theme_system
            # 检查是否还有旧的导入
        except ImportError as e:
            issues.append(f"导入错误: {e}")
        
        return issues
    
    def test_theme_switching(self):
        """测试主题切换"""
        try:
            current_mode = self.theme_system.get_current_mode()
            new_mode = ThemeMode.DARK if current_mode == ThemeMode.LIGHT else ThemeMode.LIGHT
            
            self.theme_system.set_theme_mode(new_mode)
            self.apply_theme()
            
            # 更新按钮文本
            sender = self.sender()
            if sender:
                mode_text = "🌙 深色" if new_mode == ThemeMode.DARK else "☀️ 浅色"
                sender.setText(f"{mode_text} 主题切换成功")
            
            logger.info(f"主题切换成功: {current_mode.value} -> {new_mode.value}")
            
            # 3秒后恢复按钮文本
            QTimer.singleShot(3000, lambda: sender.setText("🌙 切换主题测试") if sender else None)
            
        except Exception as e:
            logger.error(f"主题切换失败: {e}")
            sender = self.sender()
            if sender:
                sender.setText("❌ 切换失败")
                QTimer.singleShot(3000, lambda: sender.setText("🌙 切换主题测试"))


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("AI视频生成器 - 最终验证")
    app.setApplicationVersion("2.0")
    
    # 创建验证窗口
    window = FinalVerificationWindow()
    window.show()
    
    logger.info("最终验证测试启动")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()