#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一主题管理器
整合所有样式系统，提供统一的主题管理接口
支持多种配色方案和深浅色主题切换
"""

import os
from typing import Dict, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QSettings
from PyQt5.QtWidgets import QApplication, QWidget

from .color_schemes import ColorSchemes, ColorSchemeType, ThemeMode, ColorScheme
from .style_generator import StyleGenerator
from src.utils.logger import logger


class ThemeManager(QObject):
    """统一主题管理器"""

    # 信号
    theme_changed = pyqtSignal(str, str)  # scheme_name, mode_name
    color_scheme_changed = pyqtSignal(str)  # scheme_name
    mode_changed = pyqtSignal(str)  # mode_name

    _instance: Optional['ThemeManager'] = None

    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        super().__init__()
        self._initialized = True
        
        # 当前主题设置
        self.current_scheme_type = ColorSchemeType.OCEAN_BLUE
        self.current_mode = ThemeMode.LIGHT
        self.current_scheme: ColorScheme = ColorSchemes.get_scheme(
            self.current_scheme_type, self.current_mode
        )
        
        # 样式生成器
        self.style_generator = StyleGenerator()
        
        # 设置管理
        self.settings = QSettings("AIVideoGenerator", "ThemeManager")
        
        # 样式缓存
        self._style_cache: Dict[str, str] = {}
        
        # 加载保存的设置
        self.load_settings()
        
        logger.info("统一主题管理器初始化完成")
    
    def load_settings(self):
        """加载保存的主题设置"""
        try:
            # 加载配色方案
            scheme_name = self.settings.value("color_scheme", ColorSchemeType.OCEAN_BLUE.value)
            try:
                self.current_scheme_type = ColorSchemeType(scheme_name)
            except ValueError:
                self.current_scheme_type = ColorSchemeType.OCEAN_BLUE
            
            # 加载主题模式
            mode_name = self.settings.value("theme_mode", ThemeMode.LIGHT.value)
            try:
                self.current_mode = ThemeMode(mode_name)
            except ValueError:
                self.current_mode = ThemeMode.LIGHT
            
            # 更新当前配色方案
            self.current_scheme = ColorSchemes.get_scheme(
                self.current_scheme_type, self.current_mode
            )
            
            logger.info(f"已加载主题设置: {self.current_scheme.name}")
            
        except Exception as e:
            logger.warning(f"加载主题设置失败: {e}")
            self.reset_to_default()
    
    def save_settings(self):
        """保存主题设置"""
        try:
            self.settings.setValue("color_scheme", self.current_scheme_type.value)
            self.settings.setValue("theme_mode", self.current_mode.value)
            self.settings.sync()
            logger.debug("主题设置已保存")
        except Exception as e:
            logger.error(f"保存主题设置失败: {e}")
    
    def reset_to_default(self):
        """重置为默认主题"""
        self.current_scheme_type = ColorSchemeType.OCEAN_BLUE
        self.current_mode = ThemeMode.LIGHT
        self.current_scheme = ColorSchemes.get_scheme(
            self.current_scheme_type, self.current_mode
        )
        self._clear_cache()
    
    def set_color_scheme(self, scheme_type: ColorSchemeType, save: bool = True):
        """设置配色方案"""
        if scheme_type != self.current_scheme_type:
            self.current_scheme_type = scheme_type
            self.current_scheme = ColorSchemes.get_scheme(
                self.current_scheme_type, self.current_mode
            )
            self._clear_cache()
            
            if save:
                self.save_settings()
            
            # 发送信号
            scheme_name = ColorSchemes.get_scheme_names()[scheme_type]
            self.color_scheme_changed.emit(scheme_name)
            self.theme_changed.emit(scheme_name, self.current_mode.value)
            
            logger.info(f"配色方案已切换到: {self.current_scheme.name}")
    
    def set_theme_mode(self, mode: ThemeMode, save: bool = True):
        """设置主题模式（深色/浅色）"""
        if mode != self.current_mode:
            self.current_mode = mode
            self.current_scheme = ColorSchemes.get_scheme(
                self.current_scheme_type, self.current_mode
            )
            self._clear_cache()
            
            if save:
                self.save_settings()
            
            # 发送信号
            scheme_name = ColorSchemes.get_scheme_names()[self.current_scheme_type]
            self.mode_changed.emit(mode.value)
            self.theme_changed.emit(scheme_name, mode.value)
            
            logger.info(f"主题模式已切换到: {mode.value}")
    
    def toggle_theme_mode(self):
        """切换主题模式"""
        new_mode = ThemeMode.DARK if self.current_mode == ThemeMode.LIGHT else ThemeMode.LIGHT
        self.set_theme_mode(new_mode)
    
    def get_current_scheme(self) -> ColorScheme:
        """获取当前配色方案"""
        return self.current_scheme
    
    def get_color(self, key: str, fallback: str = "#000000") -> str:
        """获取当前主题的颜色"""
        return self.current_scheme.get_color(key, fallback)
    
    def get_complete_stylesheet(self) -> str:
        """获取完整的样式表"""
        cache_key = f"{self.current_scheme_type.value}_{self.current_mode.value}"
        
        if cache_key in self._style_cache:
            return self._style_cache[cache_key]
        
        # 生成样式表
        stylesheet = self.style_generator.generate_complete_stylesheet(self.current_scheme)
        
        # 缓存样式表
        self._style_cache[cache_key] = stylesheet
        
        return stylesheet
    
    def apply_to_application(self, app: Optional[QApplication] = None):
        """应用主题到整个应用"""
        if app is None:
            app = QApplication.instance()
        
        if app is None:
            logger.error("无法获取QApplication实例")
            return
        
        try:
            # 设置应用程序样式表
            stylesheet = self.get_complete_stylesheet()
            app.setStyleSheet(stylesheet)
            
            # 设置应用程序调色板（可选）
            self._setup_application_palette(app)
            
            logger.info(f"已应用主题到应用: {self.current_scheme.name}")
            
        except Exception as e:
            logger.error(f"应用主题失败: {e}")
    
    def apply_to_widget(self, widget: QWidget):
        """应用主题到指定控件"""
        try:
            stylesheet = self.get_complete_stylesheet()
            widget.setStyleSheet(stylesheet)
            logger.debug(f"已应用主题到控件: {widget.__class__.__name__}")
        except Exception as e:
            logger.error(f"应用控件主题失败: {e}")
    
    def _setup_application_palette(self, app: QApplication):
        """设置应用程序调色板"""
        try:
            from PyQt5.QtGui import QPalette, QColor
            
            palette = app.palette()
            
            # 设置主要颜色
            palette.setColor(QPalette.Window, QColor(self.get_color('background')))
            palette.setColor(QPalette.WindowText, QColor(self.get_color('on_background')))
            palette.setColor(QPalette.Base, QColor(self.get_color('surface')))
            palette.setColor(QPalette.AlternateBase, QColor(self.get_color('surface_container')))
            palette.setColor(QPalette.Text, QColor(self.get_color('on_surface')))
            palette.setColor(QPalette.Button, QColor(self.get_color('primary')))
            palette.setColor(QPalette.ButtonText, QColor(self.get_color('on_primary')))
            palette.setColor(QPalette.Highlight, QColor(self.get_color('primary_container')))
            palette.setColor(QPalette.HighlightedText, QColor(self.get_color('on_primary_container')))
            
            app.setPalette(palette)
            
        except Exception as e:
            logger.warning(f"设置应用程序调色板失败: {e}")
    
    def _clear_cache(self):
        """清除样式缓存"""
        self._style_cache.clear()
    
    def get_available_schemes(self) -> Dict[ColorSchemeType, str]:
        """获取可用的配色方案"""
        return ColorSchemes.get_scheme_names()
    
    def get_current_scheme_info(self) -> Dict[str, str]:
        """获取当前主题信息"""
        scheme_names = ColorSchemes.get_scheme_names()
        return {
            "scheme_type": self.current_scheme_type.value,
            "scheme_name": scheme_names[self.current_scheme_type],
            "mode": self.current_mode.value,
            "full_name": self.current_scheme.name
        }


# 全局主题管理器实例
_theme_manager: Optional[ThemeManager] = None


def get_theme_manager() -> ThemeManager:
    """获取全局主题管理器实例"""
    global _theme_manager
    if _theme_manager is None:
        _theme_manager = ThemeManager()
    return _theme_manager


def apply_theme_to_application(app: Optional[QApplication] = None):
    """应用主题到应用程序"""
    manager = get_theme_manager()
    manager.apply_to_application(app)


def apply_theme_to_widget(widget: QWidget):
    """应用主题到控件"""
    manager = get_theme_manager()
    manager.apply_to_widget(widget)


def set_color_scheme(scheme_type: ColorSchemeType):
    """设置配色方案"""
    manager = get_theme_manager()
    manager.set_color_scheme(scheme_type)


def set_theme_mode(mode: ThemeMode):
    """设置主题模式"""
    manager = get_theme_manager()
    manager.set_theme_mode(mode)


def toggle_theme_mode():
    """切换主题模式"""
    manager = get_theme_manager()
    manager.toggle_theme_mode()


def get_current_color(key: str, fallback: str = "#000000") -> str:
    """获取当前主题颜色"""
    manager = get_theme_manager()
    return manager.get_color(key, fallback)
