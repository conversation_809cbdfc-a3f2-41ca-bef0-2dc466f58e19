#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主题系统修复
验证统一主题系统和现代UI组件是否正常工作
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

from src.gui.styles.unified_theme_system import UnifiedThemeSystem, ThemeMode
from src.gui.modern_ui_components import MaterialButton, MaterialCard
from src.gui.ai_voice_settings_widget import AIVoiceSettingsWidget
from src.utils.logger import logger


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.theme_system = UnifiedThemeSystem()
        self.init_ui()
        self.apply_theme()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("主题系统修复测试")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎨 主题系统修复测试")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 测试Material组件
        test_card = MaterialCard()
        card_layout = QVBoxLayout(test_card)
        
        card_layout.addWidget(QLabel("Material Design 组件测试"))
        
        theme_btn = MaterialButton("切换主题")
        theme_btn.clicked.connect(self.toggle_theme)
        card_layout.addWidget(theme_btn)
        
        layout.addWidget(test_card)
        
        # 测试AI配音设置组件
        try:
            voice_settings = AIVoiceSettingsWidget()
            layout.addWidget(voice_settings)
            logger.info("AI配音设置组件加载成功")
        except Exception as e:
            error_label = QLabel(f"AI配音设置组件加载失败: {e}")
            layout.addWidget(error_label)
            logger.error(f"AI配音设置组件加载失败: {e}")
    
    def apply_theme(self):
        """应用主题"""
        try:
            self.theme_system.apply_to_widget(self)
            logger.info("主题应用成功")
        except Exception as e:
            logger.error(f"主题应用失败: {e}")
    
    def toggle_theme(self):
        """切换主题"""
        try:
            current_mode = self.theme_system.get_current_mode()
            new_mode = ThemeMode.DARK if current_mode == ThemeMode.LIGHT else ThemeMode.LIGHT
            self.theme_system.set_theme_mode(new_mode)
            self.apply_theme()
            logger.info(f"主题切换到: {new_mode.value}")
        except Exception as e:
            logger.error(f"主题切换失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("主题系统修复测试")
    app.setApplicationVersion("1.0")
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    logger.info("测试程序启动成功")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()