#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动保存和内存优化功能测试脚本
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_auto_save_manager():
    """
    测试自动保存管理器
    """
    try:
        from src.utils.auto_save_manager import AutoSaveManager
        
        logger.info("=== 测试自动保存管理器 ===")
        
        # 创建自动保存管理器
        auto_save_manager = AutoSaveManager()
        
        # 测试数据获取函数
        test_data = {"test": "data", "timestamp": time.time()}
        
        def get_test_data():
            return test_data
        
        # 创建测试目录
        test_dir = Path("test_auto_save")
        test_dir.mkdir(exist_ok=True)
        test_file = test_dir / "test_data.json"
        
        # 注册保存回调
        auto_save_manager.register_save_callback(
            'test_data',
            get_test_data,
            str(test_file),
            priority=1
        )
        
        logger.info("注册保存回调成功")
        
        # 标记数据为脏
        auto_save_manager.mark_dirty('test_data')
        logger.info("标记数据为脏")
        
        # 执行立即保存
        auto_save_manager.save_immediately()
        logger.info("执行立即保存")
        
        # 检查文件是否存在
        if test_file.exists():
            logger.info(f"保存成功，文件大小: {test_file.stat().st_size} 字节")
        else:
            logger.error("保存失败，文件不存在")
        
        # 启动自动保存
        auto_save_manager.start_auto_save()
        logger.info("启动自动保存")
        
        # 等待一段时间
        time.sleep(2)
        
        # 停止自动保存
        auto_save_manager.stop_auto_save()
        logger.info("停止自动保存")
        
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()
        if test_dir.exists():
            test_dir.rmdir()
        
        logger.info("自动保存管理器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"自动保存管理器测试失败: {e}")
        return False

def test_memory_optimizer():
    """
    测试内存优化器
    """
    try:
        from src.utils.memory_optimizer import MemoryMonitor, ImageMemoryManager
        
        logger.info("=== 测试内存优化器 ===")
        
        # 测试内存监控器
        memory_monitor = MemoryMonitor()
        memory_info = memory_monitor.get_memory_info()
        logger.info(f"内存信息: {memory_info}")
        if memory_info:
            memory_percent = memory_info['system_percent'] / 100.0
            logger.info(f"当前内存使用率: {memory_percent:.1%}")
        else:
            logger.warning("无法获取内存信息")
        
        # 测试图像内存管理器
        image_manager = ImageMemoryManager(max_cache_size_mb=100)
        
        # 模拟缓存一些数据
        test_image_data = b"fake_image_data" * 1000  # 约13KB
        
        image_manager.cache_image("test_image_1", test_image_data)
        image_manager.cache_image("test_image_2", test_image_data)
        
        # 获取缓存统计
        stats = image_manager.get_cache_stats()
        logger.info(f"缓存统计: {stats}")
        
        # 测试获取图像
        cached_image = image_manager.get_image("test_image_1")
        if cached_image:
            logger.info("图像缓存获取成功")
        else:
            logger.error("图像缓存获取失败")
        
        # 清理缓存
        image_manager.clear_cache()
        stats_after_clear = image_manager.get_cache_stats()
        logger.info(f"清理后缓存统计: {stats_after_clear}")
        
        logger.info("内存优化器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"内存优化器测试失败: {e}")
        return False

def test_notification_system():
    """
    测试通知系统
    """
    try:
        from src.gui.notification_system import show_info, show_warning
        
        logger.info("=== 测试通知系统 ===")
        
        # 注意：这些函数会显示GUI对话框，在无GUI环境下会失败
        # 这里只是测试导入是否成功
        logger.info("通知系统导入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"通知系统测试失败: {e}")
        return False

def main():
    """
    主测试函数
    """
    logger.info("开始测试自动保存和内存优化功能")
    
    results = []
    
    # 测试自动保存管理器
    results.append(test_auto_save_manager())
    
    # 测试内存优化器
    results.append(test_memory_optimizer())
    
    # 测试通知系统
    results.append(test_notification_system())
    
    # 汇总结果
    success_count = sum(results)
    total_count = len(results)
    
    logger.info(f"\n=== 测试结果汇总 ===")
    logger.info(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        logger.info("所有测试通过！")
        return True
    else:
        logger.error("部分测试失败！")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)