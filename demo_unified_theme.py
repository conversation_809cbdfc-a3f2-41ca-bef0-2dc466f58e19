#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一主题系统演示脚本
展示Material Design 3.0风格的现代化UI组件
"""

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
    QWidget, QLabel, QGridLayout, QScrollArea
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.gui.styles.unified_theme_system import UnifiedThemeSystem, ThemeMode
from src.gui.modern_ui_components import (
    MaterialButton, MaterialCard, MaterialProgressBar, MaterialSlider,
    MaterialComboBox, MaterialLineEdit, MaterialTextEdit, MaterialListWidget,
    MaterialTabWidget, FloatingActionButton, MaterialToolBar, StatusIndicator,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, MaterialGroupBox, MaterialCheckBox, MaterialRadioButton
)

class ThemeDemo(QMainWindow):
    """主题演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.theme_system = UnifiedThemeSystem()
        self.init_ui()
        self.apply_theme()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("统一主题系统演示 - Material Design 3.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建工具栏
        toolbar = MaterialToolBar()
        toolbar.setObjectName("demo_toolbar")
        
        # 主题切换按钮
        theme_btn = MaterialButton("切换主题")
        theme_btn.clicked.connect(self.toggle_theme)
        toolbar.layout.addWidget(theme_btn)
        
        # 浮动操作按钮
        fab = FloatingActionButton("+")
        fab.setToolTip("添加新项目")
        toolbar.layout.addWidget(fab)
        toolbar.layout.addStretch()
        
        main_layout.addWidget(toolbar)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 标题
        title = QLabel("Material Design 3.0 组件展示")
        title.setFont(QFont("Microsoft YaHei UI", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        scroll_layout.addWidget(title)
        
        # 创建组件展示区域
        self.create_button_demo(scroll_layout)
        self.create_input_demo(scroll_layout)
        self.create_selection_demo(scroll_layout)
        self.create_display_demo(scroll_layout)
        self.create_feedback_demo(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)
        
        # 状态栏
        status_bar = self.statusBar()
        status_indicator = StatusIndicator("active")
        status_bar.addPermanentWidget(status_indicator)
        
    def create_button_demo(self, layout):
        """创建按钮演示"""
        card = MaterialCard("按钮组件")
        card_layout = QGridLayout()
        
        # 主要按钮
        primary_btn = MaterialButton("主要按钮")
        primary_btn.setObjectName("primary_button")
        card_layout.addWidget(primary_btn, 0, 0)
        
        # 次要按钮
        secondary_btn = MaterialButton("次要按钮")
        secondary_btn.setProperty("style", "secondary")
        card_layout.addWidget(secondary_btn, 0, 1)
        
        # 轮廓按钮
        outline_btn = MaterialButton("轮廓按钮")
        outline_btn.setFlat(True)
        card_layout.addWidget(outline_btn, 0, 2)
        
        # 禁用按钮
        disabled_btn = MaterialButton("禁用按钮")
        disabled_btn.setEnabled(False)
        card_layout.addWidget(disabled_btn, 0, 3)
        
        card.setLayout(card_layout)
        layout.addWidget(card)
        
    def create_input_demo(self, layout):
        """创建输入组件演示"""
        card = MaterialCard("输入组件")
        card_layout = QGridLayout()
        
        # 文本输入框
        line_edit = MaterialLineEdit()
        line_edit.setPlaceholderText("请输入文本...")
        card_layout.addWidget(QLabel("单行输入:"), 0, 0)
        card_layout.addWidget(line_edit, 0, 1)
        
        # 多行文本框
        text_edit = MaterialTextEdit()
        text_edit.setPlaceholderText("请输入多行文本...")
        text_edit.setMaximumHeight(100)
        card_layout.addWidget(QLabel("多行输入:"), 1, 0)
        card_layout.addWidget(text_edit, 1, 1)
        
        # 下拉框
        combo_box = MaterialComboBox()
        combo_box.addItems(["选项1", "选项2", "选项3", "选项4"])
        card_layout.addWidget(QLabel("下拉选择:"), 2, 0)
        card_layout.addWidget(combo_box, 2, 1)
        
        card.setLayout(card_layout)
        layout.addWidget(card)
        
    def create_selection_demo(self, layout):
        """创建选择组件演示"""
        card = MaterialCard("选择组件")
        card_layout = QVBoxLayout()
        
        # 复选框组
        checkbox_layout = QHBoxLayout()
        checkbox1 = MaterialCheckBox("选项A")
        checkbox2 = MaterialCheckBox("选项B")
        checkbox3 = MaterialCheckBox("选项C")
        checkbox2.setChecked(True)
        checkbox_layout.addWidget(checkbox1)
        checkbox_layout.addWidget(checkbox2)
        checkbox_layout.addWidget(checkbox3)
        
        card_layout.addWidget(QLabel("复选框:"))
        card_layout.addLayout(checkbox_layout)
        
        # 单选按钮组
        radio_layout = QHBoxLayout()
        radio1 = MaterialRadioButton("选项1")
        radio2 = MaterialRadioButton("选项2")
        radio3 = MaterialRadioButton("选项3")
        radio1.setChecked(True)
        radio_layout.addWidget(radio1)
        radio_layout.addWidget(radio2)
        radio_layout.addWidget(radio3)
        
        card_layout.addWidget(QLabel("单选按钮:"))
        card_layout.addLayout(radio_layout)
        
        # 滑块
        slider = MaterialSlider(Qt.Horizontal)
        slider.setRange(0, 100)
        slider.setValue(50)
        card_layout.addWidget(QLabel("滑块:"))
        card_layout.addWidget(slider)
        
        card.setLayout(card_layout)
        layout.addWidget(card)
        
    def create_display_demo(self, layout):
        """创建显示组件演示"""
        card = MaterialCard("显示组件")
        card_layout = QVBoxLayout()
        
        # 列表组件
        list_widget = MaterialListWidget()
        list_widget.addItems(["列表项目1", "列表项目2", "列表项目3", "列表项目4"])
        list_widget.setMaximumHeight(120)
        card_layout.addWidget(QLabel("列表组件:"))
        card_layout.addWidget(list_widget)
        
        # 标签页组件
        tab_widget = MaterialTabWidget()
        tab_widget.addTab(QLabel("标签页1内容"), "标签页1")
        tab_widget.addTab(QLabel("标签页2内容"), "标签页2")
        tab_widget.addTab(QLabel("标签页3内容"), "标签页3")
        tab_widget.setMaximumHeight(150)
        card_layout.addWidget(QLabel("标签页组件:"))
        card_layout.addWidget(tab_widget)
        
        # 分组框
        group_box = MaterialGroupBox("分组框标题")
        group_layout = QVBoxLayout()
        group_layout.addWidget(QLabel("这是分组框内的内容"))
        group_layout.addWidget(MaterialButton("分组内按钮"))
        group_box.setLayout(group_layout)
        card_layout.addWidget(group_box)
        
        card.setLayout(card_layout)
        layout.addWidget(card)
        
    def create_feedback_demo(self, layout):
        """创建反馈组件演示"""
        card = MaterialCard("反馈组件")
        card_layout = QVBoxLayout()
        
        # 进度条
        progress_bar = MaterialProgressBar()
        progress_bar.setValue(75)
        card_layout.addWidget(QLabel("进度条:"))
        card_layout.addWidget(progress_bar)
        
        # 加载动画
        loading_layout = QHBoxLayout()
        loading_spinner = LoadingSpinner()
        loading_spinner.start_animation()
        loading_layout.addWidget(QLabel("加载动画:"))
        loading_layout.addWidget(loading_spinner)
        loading_layout.addStretch()
        card_layout.addLayout(loading_layout)
        
        # 状态指示器
        status_layout = QHBoxLayout()
        status_success = StatusIndicator("active")
        status_warning = StatusIndicator("warning")
        status_error = StatusIndicator("error")
        
        status_layout.addWidget(status_success)
        status_layout.addWidget(status_warning)
        status_layout.addWidget(status_error)
        status_layout.addStretch()
        
        card_layout.addWidget(QLabel("状态指示器:"))
        card_layout.addLayout(status_layout)
        
        card.setLayout(card_layout)
        layout.addWidget(card)
        
    def apply_theme(self):
        """应用主题"""
        self.theme_system.apply_to_application()
        
    def toggle_theme(self):
        """切换主题"""
        current_mode = self.theme_system.get_current_mode()
        new_mode = ThemeMode.DARK if current_mode == ThemeMode.LIGHT else ThemeMode.LIGHT
        self.theme_system.set_theme_mode(new_mode)
        self.apply_theme()
        
def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("统一主题系统演示")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AI Video Generator")
    
    # 创建并显示主窗口
    demo = ThemeDemo()
    demo.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()