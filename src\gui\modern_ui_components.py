#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化UI组件
提供Material Design风格的自定义控件
"""

from PyQt5.QtWidgets import (
    QPushButton, QFrame, QLabel, QVBoxLayout, QHBoxLayout, 
    QWidget, QGraphicsDropShadowEffect, QProgressBar, QSlider,
    QComboBox, QLineEdit, QTextEdit, QListWidget, QTabWidget
)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtProperty, QRect
from PyQt5.QtGui import QPainter, QColor, QPen, QBrush, QFont, QPalette

class MaterialButton(QPushButton):
    """Material Design风格按钮"""
    
    def __init__(self, text="", button_type="filled", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setup_style()
        self.setup_animations()
    
    def setup_style(self):
        """设置按钮样式"""
        self.setMinimumHeight(40)
        self.setFont(QFont("Segoe UI", 10, QFont.Medium))
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(8)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
    
    def setup_animations(self):
        """设置动画效果"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(150)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        # 添加悬停动画
        current_rect = self.geometry()
        new_rect = QRect(current_rect.x(), current_rect.y() - 1, 
                        current_rect.width(), current_rect.height())
        self.animation.setStartValue(current_rect)
        self.animation.setEndValue(new_rect)
        self.animation.start()
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        # 恢复原始位置
        current_rect = self.geometry()
        new_rect = QRect(current_rect.x(), current_rect.y() + 1, 
                        current_rect.width(), current_rect.height())
        self.animation.setStartValue(current_rect)
        self.animation.setEndValue(new_rect)
        self.animation.start()


class MaterialCard(QFrame):
    """Material Design风格卡片"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置卡片样式"""
        self.setFrameStyle(QFrame.NoFrame)
        self.setAttribute(Qt.WA_StyledBackground, True)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(12)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
        
        # 设置属性用于样式表
        self.setProperty("cardStyle", True)


class MaterialProgressBar(QProgressBar):
    """Material Design风格进度条"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置进度条样式"""
        self.setMinimumHeight(8)
        self.setMaximumHeight(8)
        self.setTextVisible(False)


class MaterialSlider(QSlider):
    """Material Design风格滑块"""
    
    def __init__(self, orientation=Qt.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setup_style()
    
    def setup_style(self):
        """设置滑块样式"""
        self.setMinimumHeight(20)


class MaterialComboBox(QComboBox):
    """Material Design风格下拉框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置下拉框样式"""
        self.setMinimumHeight(40)
        self.setFont(QFont("Segoe UI", 10))


class MaterialLineEdit(QLineEdit):
    """Material Design风格输入框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置输入框样式"""
        self.setMinimumHeight(40)
        self.setFont(QFont("Segoe UI", 10))


class MaterialTextEdit(QTextEdit):
    """Material Design风格文本编辑器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置文本编辑器样式"""
        self.setFont(QFont("Segoe UI", 10))


class MaterialListWidget(QListWidget):
    """Material Design风格列表"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置列表样式"""
        self.setFont(QFont("Segoe UI", 10))
        self.setAlternatingRowColors(True)


class MaterialTabWidget(QTabWidget):
    """Material Design风格标签页"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置标签页样式"""
        self.setFont(QFont("Segoe UI", 10, QFont.Medium))


class FloatingActionButton(QPushButton):
    """悬浮操作按钮"""
    
    def __init__(self, icon_text="", parent=None):
        super().__init__(icon_text, parent)
        self.setup_style()
        self.setup_animations()
    
    def setup_style(self):
        """设置FAB样式"""
        self.setFixedSize(56, 56)
        self.setFont(QFont("Segoe UI", 16))
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(16)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
    
    def setup_animations(self):
        """设置动画效果"""
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(200)
        self.scale_animation.setEasingCurve(QEasingCurve.OutBack)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        # 放大动画
        current_rect = self.geometry()
        center = current_rect.center()
        new_size = 60
        new_rect = QRect(center.x() - new_size//2, center.y() - new_size//2, 
                        new_size, new_size)
        self.scale_animation.setStartValue(current_rect)
        self.scale_animation.setEndValue(new_rect)
        self.scale_animation.start()
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        # 恢复原始大小
        current_rect = self.geometry()
        center = current_rect.center()
        new_size = 56
        new_rect = QRect(center.x() - new_size//2, center.y() - new_size//2, 
                        new_size, new_size)
        self.scale_animation.setStartValue(current_rect)
        self.scale_animation.setEndValue(new_rect)
        self.scale_animation.start()


class MaterialToolbar(QFrame):
    """Material Design风格工具栏"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
        self.setup_layout()
    
    def setup_style(self):
        """设置工具栏样式"""
        self.setFixedHeight(64)
        self.setFrameStyle(QFrame.NoFrame)
        self.setAttribute(Qt.WA_StyledBackground, True)
        
        # 添加底部阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(8)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
    
    def setup_layout(self):
        """设置布局"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(16, 8, 16, 8)
        self.layout.setSpacing(16)
    
    def add_widget(self, widget):
        """添加控件"""
        self.layout.addWidget(widget)
    
    def add_stretch(self):
        """添加弹性空间"""
        self.layout.addStretch()


class StatusIndicator(QLabel):
    """状态指示器"""
    
    def __init__(self, status="unknown", parent=None):
        super().__init__(parent)
        self.status = status
        self.setup_style()
        self.update_status(status)
    
    def setup_style(self):
        """设置样式"""
        self.setFixedSize(12, 12)
        self.setAlignment(Qt.AlignCenter)
    
    def update_status(self, status):
        """更新状态"""
        self.status = status
        colors = {
            "online": "#4CAF50",    # 绿色
            "offline": "#F44336",   # 红色
            "warning": "#FF9800",   # 橙色
            "unknown": "#9E9E9E"    # 灰色
        }
        
        color = colors.get(status, colors["unknown"])
        self.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border-radius: 6px;
            }}
        """)
        
        # 设置工具提示
        status_text = {
            "online": "在线",
            "offline": "离线", 
            "warning": "警告",
            "unknown": "未知"
        }
        self.setToolTip(status_text.get(status, "未知状态"))


class LoadingSpinner(QLabel):
    """加载动画"""
    
    def __init__(self, size=32, parent=None):
        super().__init__(parent)
        self.size = size
        self.angle = 0
        self.setup_style()
        self.setup_animation()
    
    def setup_style(self):
        """设置样式"""
        self.setFixedSize(self.size, self.size)
        self.setAlignment(Qt.AlignCenter)
    
    def setup_animation(self):
        """设置动画"""
        self.animation = QPropertyAnimation(self, b"rotation")
        self.animation.setDuration(1000)
        self.animation.setStartValue(0)
        self.animation.setEndValue(360)
        self.animation.setLoopCount(-1)  # 无限循环
    
    def start_animation(self):
        """开始动画"""
        self.animation.start()
    
    def stop_animation(self):
        """停止动画"""
        self.animation.stop()
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制圆形进度条
        rect = self.rect().adjusted(4, 4, -4, -4)
        painter.setPen(QPen(QColor("#6750A4"), 3, Qt.SolidLine, Qt.RoundCap))
        painter.drawArc(rect, self.angle * 16, 90 * 16)
    
    @pyqtProperty(int)
    def rotation(self):
        return self.angle
    
    @rotation.setter
    def rotation(self, value):
        self.angle = value
        self.update()
