#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - 优化启动脚本
提供智能环境检测、依赖安装和错误处理
"""

import sys
import os
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: Python {version.major}.{version.minor}.{version.micro}")
        print("请升级Python版本后重试")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True


def check_virtual_environment():
    """检查是否在虚拟环境中"""
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    )
    
    if in_venv:
        print("✅ 检测到虚拟环境")
        return True
    else:
        print("⚠️  未检测到虚拟环境")
        print("建议在虚拟环境中运行以避免依赖冲突")
        
        response = input("是否继续？(y/n): ").lower().strip()
        return response in ['y', 'yes']


def install_dependencies():
    """安装依赖包"""
    print("\n📦 检查并安装依赖包...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ 错误: requirements.txt 文件不存在")
        return False
    
    try:
        # 升级pip
        print("升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装依赖
        print("安装依赖包...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                               check=True, capture_output=True, text=True)
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        
        # 尝试逐个安装关键依赖
        critical_packages = ["PyQt5", "requests", "Pillow", "numpy"]
        print("\n尝试安装关键依赖包...")
        
        for package in critical_packages:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              check=True, capture_output=True)
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                
        return False


def check_config_files():
    """检查配置文件"""
    print("\n⚙️  检查配置文件...")
    
    config_dir = Path("config")
    required_configs = [
        "llm_config.json",
        "tts_config.json",
        "app_settings.json"
    ]
    
    missing_configs = []
    for config_file in required_configs:
        config_path = config_dir / config_file
        example_path = config_dir / f"{config_file.replace('.json', '.example.json')}"
        
        if not config_path.exists():
            if example_path.exists():
                print(f"⚠️  {config_file} 不存在，从示例文件创建...")
                try:
                    import shutil
                    shutil.copy2(example_path, config_path)
                    print(f"✅ 已创建 {config_file}")
                except Exception as e:
                    print(f"❌ 创建 {config_file} 失败: {e}")
                    missing_configs.append(config_file)
            else:
                missing_configs.append(config_file)
    
    if missing_configs:
        print(f"❌ 缺少配置文件: {', '.join(missing_configs)}")
        print("请参考文档配置相关文件")
        return False
    
    print("✅ 配置文件检查完成")
    return True


def optimize_system():
    """系统优化"""
    print("\n🚀 执行系统优化...")
    
    try:
        # 设置环境变量
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
        os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'
        
        # Windows特定优化
        if platform.system() == "Windows":
            # 设置DPI感知
            try:
                import ctypes
                ctypes.windll.shcore.SetProcessDpiAwareness(1)
            except:
                pass
        
        print("✅ 系统优化完成")
        return True
        
    except Exception as e:
        print(f"⚠️  系统优化失败: {e}")
        return True  # 优化失败不影响启动


def start_application():
    """启动应用程序"""
    print("\n🎬 启动AI视频生成器...")
    
    try:
        # 导入主程序
        from main import main
        
        # 启动应用
        main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请检查项目文件是否完整")
        return False
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("🎬 AI视频生成器 - 智能启动器")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查虚拟环境
    if not check_virtual_environment():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("\n⚠️  依赖安装不完整，但将尝试启动程序")
        response = input("是否继续启动？(y/n): ").lower().strip()
        if response not in ['y', 'yes']:
            sys.exit(1)
    
    # 检查配置文件
    if not check_config_files():
        print("\n⚠️  配置文件不完整，程序可能无法正常工作")
        response = input("是否继续启动？(y/n): ").lower().strip()
        if response not in ['y', 'yes']:
            sys.exit(1)
    
    # 系统优化
    optimize_system()
    
    # 启动应用
    if not start_application():
        print("\n❌ 程序启动失败")
        print("请检查错误信息并参考文档解决问题")
        sys.exit(1)


if __name__ == "__main__":
    main()
